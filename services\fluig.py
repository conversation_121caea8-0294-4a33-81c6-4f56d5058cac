import requests
import services.vars as vars
import xmltodict
from typing import Tuple
import json

def create_folder(namefolder:str)->str:
    """Método responsável por criar uma pasta no fluig via Webservices

    Args:
        namefolder (str): Nome da pasta que irá ser criada

    Returns:
        str: Identificador do documento retornado pelo webservices
    """
    url = vars.ecmfolder

    payload = vars.createfolderfluig.format(vars.usernamefluig,vars.passwordfluig,vars.dadfolderfluig,vars.publisheridfluig,namefolder)
    headers = {
    'Content-Type': 'application/xml',
    'Cookie': 'JSESSIONID="YapCtVbhdGZV8s26dDFVCL299ObIIu7W-3zp4Jlw.master:129690-core-instance-N-FL-D-CN34TY-1-00f4cLIN-CE01"'
    }

    try:
        response = requests.request("POST", url, headers=headers, data=payload)
    except Exception as error:
        print("Erro ao requisitar criação de pasta no fluig")
        print(f"Detalhe do erro: {error}")
        raise error

    doc = xmltodict.parse(response.text)
    
    return doc['soap:Envelope']['soap:Body']['ns1:createSimpleFolderResponse']['result']['item']['documentId']



def upload_document(namefolder:str,arquivo:str,id_pasta:int)->str:
    """Método responsável por relizar upload do documento para o fluig via webservices

    Args:
        namefolder (str): Nome da pasta
        arquivo (str): nome do arquivo
        id_pasta (int): Id da pasta no fluig

    Returns:
        str: Identificador do documento retornado pelo webservices
    """
    url = vars.ecmdocument

    payload = vars.uploaddocumentfluig.format(vars.usernamefluig,vars.passwordfluig,int(id_pasta),vars.publisheridfluig,namefolder,namefolder,arquivo)
 
    headers = {
    'Content-Type': 'application/xml',
    'Cookie': 'JSESSIONID="GZBbaej_EHFBhtbVtD2RK0UGoVGlkxTpMITOikIR.master:129690-core-instance-N-FL-D-CN34TY-1-00f4cLIN-CE01"'
    }

    try:
        response = requests.request("POST", url, headers=headers, data=payload)

        doc = xmltodict.parse(response.text)
       # print(f"Resposta do Fluig: {response.text}")
        #print(f"Número do documento: {doc['soap:Envelope']['soap:Body']['ns1:createSimpleDocumentResponse']['result']['item']['documentId']}")
        return doc['soap:Envelope']['soap:Body']['ns1:createSimpleDocumentResponse']['result']['item']['documentId']
    except Exception as error:
        print("Erro fazer o upload da carta no fluig")
        print(f"Detalhes do erro: {error}")
        raise error


def delete_documents(id_list:list)->Tuple[int, str]:
    
    url = "https://pmenos.fluig.cloudtotvs.com.br/ecm/api/rest/ecm/navigation/removeDoc/"
    
    body = vars.deleteMassiveFluig
    for i in id_list:
        #
        body["docsToDelete"].append({"docId": str(i[0]),"isLink": False,"parentId":i[2]})
        #
    print(json.dumps(body))
    headers = {
  'Authorization': 'Bearer ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
  'Content-Type': 'application/json',
  'Cookie': 'JSESSIONID="WTbadvON5fxSBK1tdqXoHGSFm5HCfKfbUqPKUjGO.master:129727-core-instance-N-FL-P-CN34TY-1-3ae74LIN-CE01"; JSESSIONIDSSO=8JrzkJq3wI7qC0r4Kah-BW_CGY_2aAF6y1s8fblJ'
    }

    response = requests.request("DELETE", url, headers=headers, data=json.dumps(body))

    print(response.text)
    return 0,'sucess'


def delete_document(id_arquivo:int)->Tuple[int, str]:
    """Método responsável por deletar carta do fluig a partir de um identificador 

    Args:
        id_arquivo (int): Identificador do documento
        
    Returns:
        Tuple[int, str]: Tupla com o código do erro e mensagem
    """
    url = vars.ecmdocument
    
    payloadDelete = vars.deletefluig.format('deleteDocument',vars.usernamefluig,vars.passwordfluig,int(id_arquivo),vars.publisheridfluig,'deleteDocument')

    payloadDestroy = vars.deletefluig.format('destroyDocument',vars.usernamefluig,vars.passwordfluig,int(id_arquivo),vars.publisheridfluig,'destroyDocument')
 
    headers = {
    'Content-Type': 'application/xml',
    'Cookie': 'JSESSIONID="GZBbaej_EHFBhtbVtD2RK0UGoVGlkxTpMITOikIR.master:129690-core-instance-N-FL-D-CN34TY-1-00f4cLIN-CE01"'
    }

    try:
        responseDelete = requests.request("POST", url, headers=headers, data=payloadDelete)
        docDelete = xmltodict.parse(responseDelete.text)
    except Exception as error:
        print("Erro na primeira parte do exclusão")
        print(f"Detalhes do erro: {error}")
        raise error
    
    respDelete = docDelete['soap:Envelope']['soap:Body']['ns1:deleteDocumentResponse']['result']['item']['webServiceMessage']
    
    if respDelete == 'ok':
        try:
            responseDestroy = requests.request("POST", url, headers=headers, data=payloadDestroy)
            docDestroy = xmltodict.parse(responseDestroy.text)
        except Exception as error:
            print("Erro na segunda parte parte do exclusão")
            print(f"Detalhes do erro: {error}")
            raise error
        
        respDestroy = docDestroy['soap:Envelope']['soap:Body']['ns1:destroyDocumentResponse']['result']['item']['webServiceMessage']
        if respDestroy == 'ok':
            return 0, respDestroy       
        else:
            return -2, respDestroy
    else:
        return -1, respDelete    

