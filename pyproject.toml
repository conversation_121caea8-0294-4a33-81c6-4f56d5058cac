[project]
name = "rpa-cartas-desfazimento"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "ahk==0.14.2",
    "annotated-types==0.7.0",
    "attrs==23.2.0",
    "autoit==0.2.6",
    "botcity-maestro-sdk==0.5.1",
    "certifi==2024.6.2",
    "cffi==1.16.0",
    "charset-normalizer==3.3.2",
    "clipboard==0.0.4",
    "colorama==0.4.6",
    "cryptography==41.0.2",
    "distro==1.9.0",
    "exceptiongroup==1.2.1",
    "h11==0.14.0",
    "idna==3.7",
    "importlib-metadata==7.1.0",
    "jinja2==3.1.2",
    "markupsafe==2.1.2",
    "mouseinfo==0.1.3",
    "numpy==1.24.4",
    "outcome==1.3.0.post0",
    "packaging==24.1",
    "pandas==1.5.3",
    "pillow==10.3.0",
    "pyasn1==0.6.0",
    "pyautogui==0.9.53",
    "pycparser==2.22",
    "pydantic==2.10.2",
    "pydantic-core==2.27.1",
    "pygetwindow==0.0.9",
    "pymsgbox==1.0.9",
    "pyodbc==4.0.39",
    "pypdf2==3.0.1",
    "pyperclip==1.8.2",
    "pyrect==0.2.0",
    "pyscreeze==0.1.28",
    "pysmb==*******",
    "pysocks==1.7.1",
    "pytest>=8.4.0",
    "python-dateutil==2.9.0.post0",
    "python-dotenv==1.0.0",
    "pytweening==1.0.4",
    "pytz==2024.1",
    "requests==2.32.3",
    "requests-toolbelt==1.0.0",
    "selenium==4.9.1",
    "six==1.16.0",
    "sniffio==1.3.1",
    "sortedcontainers==2.4.0",
    "tqdm==4.66.4",
    "trio==0.25.1",
    "trio-websocket==0.11.1",
    "typing-extensions==4.12.2",
    "urllib3==2.2.2",
    "webdriver-manager==4.0.1",
    "wsproto==1.2.0",
    "xlsxwriter==3.1.2",
    "xmltodict==0.13.0",
    "zipp==3.19.2",
]
