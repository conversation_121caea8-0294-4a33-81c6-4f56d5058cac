from typing import List, Union
from time import perf_counter
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as chrome_options
from selenium.webdriver.firefox.options import Options as firefox_options
from selenium.webdriver.edge.options import Options as edge_options

from selenium.webdriver.chrome.service import Service as chrome_service
from selenium.webdriver.firefox.service import Service as fire_servive
from selenium.webdriver.edge.service import Service as edge_service

from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
from webdriver_manager.microsoft import EdgeChromiumDriverManager

from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.support import expected_conditions as EC

from .logger import create_logger
from .webelementprocess import WebElementProcess
from .web_lib_utils import ResultHandle
from selenium.webdriver.remote.remote_connection import LOGGER as seleniumLogger
from urllib3.connectionpool import log as urllibLogger

if __name__ == "__main__":
    web_lib_logger = create_logger(__name__)
    web_lib_logger.setLevel(10)
else:
    web_lib_logger = create_logger(__name__, without_handler = True)

urllibLogger.setLevel(30)
seleniumLogger.setLevel(30)
    

class WebLibError(Exception):
    def __init__(self, message: object) -> None:
        super().__init__(message)


class WebMaster:


    def __init__(self, 
        driver_brand: str, 
        options: Union[firefox_options, chrome_options, edge_options] = None, 
        profile: webdriver.FirefoxProfile = None) -> None:
        """# Controlador das atividades do navegador

        Args:
            driver_brand (str): `chrome`, `firefox`, `edge`
            options (Union[firefox_options, chrome_options, edge_options], optional): _description_. Defaults to None.
            profile (Union[webdriver.FirefoxProfile], optional): _description_. Defaults to None.

        Raises:
            NotImplementedError: _description_
        """

        self.driver: webdriver
        self.current_screen: str

        if driver_brand == "chrome": self._get_chrome_driver(options)
        elif driver_brand == "firefox": self._get_firefox_driver(options, profile)
        elif driver_brand == "edge": self._get_edge_driver(options)
            
        else:

            msg = f"Marca de navergador {driver_brand} não está contemplada"
            web_lib_logger.error(msg)
            raise NotImplementedError(msg)
        
    
    def _get_chrome_driver(self, options: Union[chrome_options, None]):

        if  options is None:

            self.driver: webdriver.Chrome = webdriver.Chrome(
            service = chrome_service(ChromeDriverManager().install()))

        else:

            self.driver: webdriver.Chrome = webdriver.Chrome(
                service = chrome_service(ChromeDriverManager().install()), 
                options = options)
    
    def _get_firefox_driver(self, options: Union[firefox_options, None], profile: Union[webdriver.FirefoxProfile, None]):

        if  options is None and profile is None:

            self.driver: webdriver.Firefox = webdriver.Firefox(
            service = fire_servive(GeckoDriverManager().install()))
        
        elif options is None:

            self.driver: webdriver.Firefox = webdriver.Firefox(
            service = fire_servive(GeckoDriverManager().install()), 
            firefox_profile = profile)
        
        elif profile is None:

            self.driver: webdriver.Firefox = webdriver.Firefox(
            service = fire_servive(GeckoDriverManager().install()), 
            options = options)

        else:

            self.driver: webdriver.Firefox = webdriver.Firefox(
                service = fire_servive(GeckoDriverManager().install()), 
                options = options,
                firefox_profile = profile)

    def _get_edge_driver(self, options: Union[chrome_options, None]):

        if options is None:

            self.driver: webdriver.Edge = webdriver.Edge(
            service = edge_service(EdgeChromiumDriverManager().install()))
        
        else:

            self.driver: webdriver.Edge = webdriver.Edge(
                service = edge_service(EdgeChromiumDriverManager().install()), 
                options = options)


    def write_in_field(self, 
        input: str, web_ele_proces: WebElementProcess, 
        timeout: float = 25,
        raise_approach: bool = True) -> Union[None, ResultHandle]:

        identifier = web_ele_proces.identifier
        kind_identifier = web_ele_proces.kind_identifier

        web_lib_logger.debug(f"Escrevendo em: {web_ele_proces.label}")
        try:
            element: WebElement = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((kind_identifier, identifier))
                )
            
            element.send_keys(input)

        except Exception as error:
            msg = f"Erro ao escrever no elemento |{web_ele_proces.label}| da tela |{self.current_screen}|"
            return self._deal_with_error(msg, error, raise_approach)

    def click_on_element(self, 
        web_ele_proces: WebElementProcess, 
        timeout: float = 25,
        raise_approach: bool = True) -> Union[WebElement, ResultHandle]:

        identifier = web_ele_proces.identifier
        kind_identifier = web_ele_proces.kind_identifier

        web_lib_logger.debug(f"Clicando em: {web_ele_proces.label}")
        try:
            element: WebElement = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((kind_identifier, identifier))
                )
            
            element.click()

        except Exception as error:
            msg = f"Erro ao clicar no elemento |{web_ele_proces.label}| da tela |{self.current_screen}|"
            return self._deal_with_error(msg, error, raise_approach)
    
    def locate_element(self, 
        web_ele_proces: WebElementProcess, 
        timeout: float = 25,
        raise_approach: bool = True) -> Union[WebElement, ResultHandle]:
        
        identifier = web_ele_proces.identifier
        kind_identifier = web_ele_proces.kind_identifier

        web_lib_logger.debug(f"Procurando pelo elemento: {web_ele_proces.label}")
        try:
            element: WebElement = WebDriverWait(self.driver, timeout).until(
                EC.visibility_of_element_located((kind_identifier, identifier))
                )
            
            return element


        except Exception as error:
            msg = f"Elemento |{web_ele_proces.label}| da tela |{self.current_screen}| não foi encontrado"
            return self._deal_with_error(msg, error, raise_approach)
        
    def locate_element_in_another_element(self, 
            outer_element: WebElement, inner_ele: WebElementProcess, 
            single: bool = True,
            raise_approach: bool = True) -> Union[WebElement, ResultHandle]:

            try:
                
                if single: return outer_element.find_element(inner_ele.kind_identifier, inner_ele.identifier)
                else: return outer_element.find_elements(inner_ele.kind_identifier, inner_ele.identifier)
                
            except Exception as error:
                msg = f"Elemento |{inner_ele.label}| não foi encontrado dentro do elemento |{self.current_screen}| da tela |{self.current_screen}|"
                return self._deal_with_error(msg, error, raise_approach)
    
    def locate_all_elements(self, 
        web_ele_proces: WebElementProcess, 
        timeout: float = 25,
        raise_approach: bool = True) -> List[WebElement]:
        
        identifier = web_ele_proces.identifier
        kind_identifier = web_ele_proces.kind_identifier

        web_lib_logger.debug(f"Procurando pelo elemento {web_ele_proces.label}")
        try:
            elements: List[WebElement] = WebDriverWait(self.driver, timeout).until(
                EC.visibility_of_all_elements_located((kind_identifier, identifier))
                )
            
            return elements


        except Exception as error:
            msg = f"Elemento |{web_ele_proces.label}| da tela |{self.current_screen}| não foi encontrado"
            return self._deal_with_error(msg, error, raise_approach)
    
    def _deal_with_error(self, msg: str, error: Exception, raise_approach: bool) -> ResultHandle:

        web_lib_logger.warning(msg)
        if raise_approach:
            raise WebLibError(msg)
        else:
            return ResultHandle.Fail(msg, error)

    
    def change_tab(self, tab_hash: str):
        
        for current_tab_hash in self.driver.window_handles:
            if current_tab_hash == tab_hash:
                self.driver.switch_to.window(tab_hash)
                return
        
        web_lib_logger.info(f"Hash tab {tab_hash} não  foi encontrado no driver")
    
    def get_the_new_tab_hash(self, old_tab_hashes: List[str]) -> Union[str, None]:

        new_tab_hash = list(set(self.driver.window_handles) - set(old_tab_hashes))

        if len(new_tab_hash) == 0:
            web_lib_logger.info("Nenhuma aba foi adicionada")
            return None
        
        return new_tab_hash[0]
    
    def wait_change_in_tabs_quant(self, timeout: int = 10):

        initial_qtd_of_tabs = len(self.driver.window_handles)
        start_time = perf_counter()
        while initial_qtd_of_tabs >= len(self.driver.window_handles):
            current_time = perf_counter()

            if current_time - start_time >= timeout:
                web_lib_logger.warning("Tempo máximo na esperada da mudança da quantidade de abas é foi ultrapassado")
                return -1

        web_lib_logger.debug(f"Quantidade de abas mudou de {initial_qtd_of_tabs} para {len(self.driver.window_handles)}")
        return 0
    
    def wait_change_in_url(self, target_url: str, timeout: int = 20) -> bool:

        initial_url = self.driver.current_url
        start_time = perf_counter()

        while self.driver.current_url != target_url:
            current_time = perf_counter()

            if current_time - start_time >= timeout:
                web_lib_logger.warning("Tempo máximo na esperada da mudança na URL foi ultrapassado")
                return False
        
        web_lib_logger.debug(f"Mudança da URL {initial_url} para {target_url}")
        return True
        






if __name__ == "__main__":
    pass