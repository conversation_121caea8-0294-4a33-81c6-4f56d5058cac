from typing import List
from pathlib import Path
from multiprocessing import Queue
from modules.logger import root_logger_with_queue_for_worker
from logging import getLogger, Logger
from services.letters_dealer.letters_op import upload_letters
from modules.logger import create_logger, file_log_listener_for_processes, get_log_file, is_there_a_file_handler
from services.vars import temp_folder
from services.utils import divide_in_groups

from multiprocessing import Process, Queue
from typing import List


if __name__ == "__main__":
    letters_mult_logger = create_logger("")
    letters_mult_logger.setLevel(10)
else:
    letters_mult_logger = create_logger(__name__, without_handler = True)



def upload_letters_in_multi_processes(qtd_workers: int):

    letters_mult_logger.info("Iniciando upload de cartas")

    fila = Queue()
    queue_process_controller = Process(target=file_log_listener_for_processes, args=(fila, get_log_file(), letters_mult_logger.level))
    queue_process_controller.start()

    
    all_letters_downloaded = list(temp_folder.iterdir())

    if len(all_letters_downloaded) == 0: 
        letters_mult_logger.info("Sem cartas para fazer upload")
        return 0

    letters_for_tasks = divide_in_groups(all_letters_downloaded, qtd_workers)

    tasks: List[Process] = []
    for worker_id, letters_to_load in enumerate(letters_for_tasks, 1):

        task = Process(target = upload_letters_worker, args=(letters_to_load, 
                                                fila,
                                                worker_id,
                                                letters_mult_logger.level))
        tasks.append(task)
        task.start()
    
    for task in tasks: task.join()
        
        
    fila.put(None)
    queue_process_controller.join()

    update_cursor_of_root_file_handler(letters_mult_logger.root)

    return 0

def upload_letters_worker(arqs: List[Path], queue: Queue, worker_id: int, master_level: int):

    root_logger_with_queue_for_worker(master_level, queue)

    logger = getLogger(f'Worker-{worker_id}')
    logger.info(f'Iniciando Worker {worker_id} de upload')

    try:
        upload_letters(arqs)
    except Exception as e:
        logger.error(f"Erro no worker {worker_id}")
        logger.error(f"Detalhe do erro: {e}")

    logger.info(f'Finalizando Worker {worker_id} de upload')


def update_cursor_of_root_file_handler(root_logger: Logger) -> int:

    main_file_handler = is_there_a_file_handler(root_logger)
    if main_file_handler is None:
        letters_mult_logger.error("Root logger não possui um file Handler")
        return -1

    with open(get_log_file(), "a") as f:
        current_cursor_position = f.tell()

    main_file_handler.stream.seek(current_cursor_position)

    return 0
