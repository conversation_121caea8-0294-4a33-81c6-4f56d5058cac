

from selenium.webdriver.common.by import By
from modules.weblib.webelementprocess import WebElementProcess

LOGIN_SCREEN = "Tela de Login"
STATUS_LETTER_SCREEN = "Tela das cartas de desfazimento"

allow_cookies_btn = WebElementProcess( 
                                     '//a[contains(text(), "Permitir")]', 
                                     By.XPATH, 
                                     "Botão de permitir Cookies")



access_credential_menu_btn = WebElementProcess(
                                        '//button[contains(text(), "Acessar minha conta")]', 
                                        By.XPATH, 
                                        "Botão para acessar conta getnet")

user_field = WebElementProcess(
                            '//*[@id="username"]', 
                            By.XPATH, 
                            "Campo para colocar o nome do usuário")

password_field = WebElementProcess(
                                '//*[@id="password"]', 
                                By.XPATH, 
                                "Campo para colocar senha")


access_account_btn = WebElementProcess(
                                '//button[contains(text(), "Acessar")]', 
                                By.XPATH, 
                                "Botão para acessar conta")

open_establisment_selector = WebElementProcess(
                                '//*[@id="shepherdHeaderEstabelecimentos"]', 
                                By.XPATH, 
                                "Botão para abrir menu de seleção de estabelecimento")

establisment_field = WebElementProcess(
                                '//*[@id="codigo"]', 
                                By.XPATH, 
                                "Campo do código de estabelecimento")


search_establisment_btn = WebElementProcess(
                                '//button[contains(text(), "Buscar")]', 
                                By.XPATH, 
                                "Botão para pesquisar código de estabelecimento")

found_establisment_template = WebElementProcess(
                                "//p[contains(text(), '{ec}')]", 
                                By.XPATH, 
                                "Linha do código de estabelecimento")

sale_date = WebElementProcess(
                            '//*[@id="data"]', 
                            By.XPATH, 
                            "Campo da data da venda")

search_letters_btn = WebElementProcess(
                                '//button[contains(text(), "buscar")]', 
                                By.XPATH, 
                                "Botão para pesquisar cartas de desfazimento")


download_lettter_btn = WebElementProcess(
                                '//em[@class="ic ic-carta-status home cursor-pointer"]', 
                                By.XPATH, 
                                "Botão para fazer download da carta de desfazimento")


download_erro_btn = WebElementProcess(
                                '//button[contains(text(), "OK, Entendi")]', 
                                By.XPATH, 
                                "Botão de erro no download")


without_letters_evidence = WebElementProcess(
                                '//div[contains(text(), "Transação inexistente")]', 
                                By.XPATH, 
                                "Mensagem da não existência de cartas")


#Adicionar estabelecimento