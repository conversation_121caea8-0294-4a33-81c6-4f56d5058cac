from pathlib import Path
import sys
import re
import csv
from time import sleep
from copy import deepcopy
from datetime import datetime
from typing import List, Union
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.remote.remote_connection import LOGGER as seleniumLogger
from urllib3.connectionpool import log as urllibLogger

#Relative Path setup
num_dir_to_src = 2
src_folder = Path(__file__)
for i in range(num_dir_to_src): src_folder = src_folder.parent
if src_folder not in sys.path: sys.path.append(str(src_folder))
if Path.cwd() not in sys.path: sys.path.append(Path.cwd().__str__())


#Relative imports
from Executers.logger import LoggerCreater
import Config.vars as vars
from Config.vars import  PATHS
import Config.site_elements as site_ele
from Connections.search_db import SearchDb
from Connections.update_db import UpdateDb
from Models.processmentinfo import ProcessmentInfo
from Models.webelementprocess import WebElementProcess
from Models.demandedinvoice import DemandedInvoice
from Executers.demand_verifier import DemandVerifier
from Executers.apportionment.runner_apportion import ApportionRunner

#Imports loggers
seleniumLogger.setLevel(LoggerCreater.get_levels().info.value)
urllibLogger.setLevel(LoggerCreater.get_levels().info.value)
if __name__ == "__main__":downloader_logger = LoggerCreater('').create_logger()
else: downloader_logger = LoggerCreater(__name__).create_logger()

researcher = SearchDb()
updater = UpdateDb()

class InvoicesDownloader:

    def __init__(self, redownload: bool = True) -> None:
        self.driver: webdriver
        self.current_screen: str
        self.redownload = redownload
        self.path_option: str
    
    def execute(self, demand: List[DemandedInvoice], path_option: str = "prod"):

        self.path_option = path_option
        try:
            self._create_driver()
            self._login()
            self._acess_filter_screen()
            self._fill_form(demand)
            self.driver.quit() 
            return 0
        except Exception as error:
            downloader_logger.error(f"Erro ao baixar fatura. {error}")
            downloader_logger.debug(f"Detalhes do erro: {error}", exc_info=True)
            return -3
            
    def _create_driver(self):
        download_folder = src_folder / PATHS[self.path_option]["temp"]

        options_driver = Options()
        options_driver.add_experimental_option("prefs",
            {
                "download.default_directory": download_folder.__str__(),
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": False,
            },
        )
        options_driver.add_argument('--log-level=3')
        options_driver.add_argument("--start-maximized")

        self.driver: webdriver.Chrome = webdriver.Chrome(
            service = Service(ChromeDriverManager().install()), 
            options =options_driver)
        
        
    def _login(self):

        self.current_screen = "login"
        self.driver.get(vars.oi_site)
        downloader_logger.info(f"O robô está na tela de {self.current_screen}")

        self._write_in_field(vars.oi_site_user, site_ele.user_field)
        self._write_in_field(vars.oi_site_password, site_ele.password_field)
        self._click_on_element(site_ele.login_button)

        downloader_logger.info("Login realizado com sucesso")
        self.current_screen = "Homepage"
    
    def _acess_filter_screen(self):

        downloader_logger.info("Acessando tela do filtro")
        self._click_on_element(site_ele.button_to_acess_filter)
        self.current_screen = "Filterscreen"
    
    def _write_in_field(self, 
        input: str, web_ele_proces: WebElementProcess, 
        timeout: float = 25):

        identifier = web_ele_proces.identifier
        kind_identifier = web_ele_proces.kind_identifier

        downloader_logger.debug(f"Escrevendo em {web_ele_proces.label}")
        try:
            element: WebElement = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((kind_identifier, identifier))
                )
            
            element.send_keys(input)

        except Exception as error:
            downloader_logger.error(f"Erro na tela {self.current_screen} no elemento |{web_ele_proces.label}|")
            downloader_logger.error(f"Breve descrição do erro {error}")
            raise error

    def _click_on_element(self, 
        web_ele_proces: WebElementProcess, 
        timeout: float = 25):

        identifier = web_ele_proces.identifier
        kind_identifier = web_ele_proces.kind_identifier

        downloader_logger.debug(f"Clicando em {web_ele_proces.label}")
        try:
            element: WebElement = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((kind_identifier, identifier))
                )
            
            element.click()

        except Exception as error:
            downloader_logger.error(f"Erro na tela {self.current_screen} no elemento |{web_ele_proces.label}|")
            downloader_logger.error(f"Breve descrição do erro {error}")
            raise error
    
    def _locate_element(self, 
        web_ele_proces: WebElementProcess, 
        timeout: float = 25) -> Union[WebElement, int]:
        
        identifier = web_ele_proces.identifier
        kind_identifier = web_ele_proces.kind_identifier

        downloader_logger.debug(f"Procurando pelo elemento {web_ele_proces.label}")
        try:
            element: WebElement = WebDriverWait(self.driver, timeout).until(
                EC.visibility_of_element_located((kind_identifier, identifier))
                )
            
            return element


        except Exception as error:
            downloader_logger.warning(f"Elemento |{web_ele_proces.label}| da tela {self.current_screen} não encontrado")
            downloader_logger.error(f"Breve descrição do erro {error}")
            return -1
    
    def _locate_element_in_another_element(self, 
            outer_element: WebElement, inner_ele: WebElementProcess, 
            single: bool = True) -> WebElement:

            try:
                
                if single: return outer_element.find_element(inner_ele.kind_identifier, inner_ele.identifier)
                else: return outer_element.find_elements(inner_ele.kind_identifier, inner_ele.identifier)
                
            except Exception as error:
                downloader_logger.error(f"Erro na tela {self.current_screen} no elemento |{inner_ele.label}|")
                downloader_logger.error(f"Breve descrição do erro {error}")

    def _fill_form(self, demand: List[DemandedInvoice]):
        
        for invoice in demand:
            
            try:
                self.driver.refresh()
                date_to_filter = invoice.period.strftime("%b/%Y").upper()
                downloader_logger.info(f"Filtrando por fatura {invoice.contract} do período {date_to_filter}")
                
                self._write_in_field(date_to_filter, site_ele.period_field)
                self._write_in_field("Todos" + Keys.RETURN, site_ele.invoice_status_field)
                
                self._click_on_element(site_ele.button_to_more_filter_options) 
                self._write_in_field("Nº de contrato" + Keys.RETURN, site_ele.filter_by)
                self._write_in_field(invoice.contract, site_ele.contract_field)
                self._click_on_element(site_ele.filter_button)
                
                filter_result =  self._verify_available_invoices(site_ele.table_result)

                if (filter_result != False or filter_result.aria_role == "table"):
                    self._download_invoice(filter_result, invoice)
                else:
                    downloader_logger.info("Fatura não disponível para download")
                    self._keep_not_available_invoices(invoice)

                self._click_on_element(site_ele.clean_button)

            except:
                downloader_logger.warning(f"Erro ao baixar fatura {invoice.contract}")
                downloader_logger.warning(f"Próxima fatura será executada")

    def _keep_not_available_invoices(self, invoice: DemandedInvoice):

        demands_folder = src_folder / PATHS[self.path_option]["demands"]
        not_available_invoices = demands_folder / "Não disponíveis.txt"

        row = [[invoice.contract, invoice.region, invoice.uf, invoice.period.strftime("%b/%Y")]]
        if not_available_invoices.exists():
            with open(not_available_invoices.__str__(), 'a', newline='') as csvfile:
                csv_writer = csv.writer(csvfile)
                csv_writer.writerow(row)
        else:
            with open(not_available_invoices.__str__(), 'w', newline='') as csvfile:
                csv_writer = csv.writer(csvfile)
                csv_writer.writerow(row)

    def _verify_available_invoices(self,
            web_process_element: WebElementProcess)-> Union[WebElement, bool]:
        
        filter_result =  self._locate_element(web_process_element)

        if filter_result == -1:           
            return False
        else:
            return filter_result
             
        
    def _download_invoice(self, 
        filter_result: WebElement, invoice: DemandedInvoice):
        
        body_rows = self._get_table_body_rows(filter_result)
        processment_info = researcher.processment_info(invoice.period, invoice.contract)
        
        if body_rows == -1:
            downloader_logger.info("Fatura não disponível para download")
            self._keep_not_available_invoices(invoice)

        for row in range(1, (body_rows + 1), 1):
            
            if invoice.region == "R1":
                self._choose_download_options(row, "csv", processment_info)
            else:
                self._choose_download_options(row, "csv", processment_info)
                self._choose_download_options(row, "pdf", processment_info)
            
    def _get_table_body_rows(self, filter_result: WebElement) -> int:
        
        lines: List[WebElement] = self._locate_element_in_another_element(filter_result, 
                                                        site_ele.find_rows_in_table, 
                                                        single=False)

        wait_table_loading()
        true_lines = list(
                        filter(
                            lambda row: row.aria_role.find("row") != -1,lines
                        ))
        
        # primeira linha verdadeira é o cabeçário
        body_lines = len(true_lines) - 1
        
        return body_lines
       
    def _choose_download_options(self, row: int, kind_file: str,
        processment_info: ProcessmentInfo):
        
        line_in_body = deepcopy(site_ele.status_column_element)
        line_in_body.set_element_index(row)

        invoice_site_status = self._locate_element(line_in_body).text
        downloader_logger.debug(f"Status atual da fatura: {processment_info.invoice_status}")
        is_sth_to_download = False

        if ((invoice_site_status == "Em aberto" and processment_info.invoice_status == "P")
            or (self.redownload == True and invoice_site_status == "Em aberto" and processment_info.invoice_status != "P")):
            
            downloader_logger.info(f"Fatura em aberto será baixada")
            self._download_by_option(row, kind_file, processment_info.id_fatura)
            is_sth_to_download = True

        if (invoice_site_status == "Paga") and (self.redownload == True):
            
            downloader_logger.info(f"Fatura Paga, mas será baixada mais uma vez")
            self._download_by_option(row, kind_file, processment_info.id_fatura)
            is_sth_to_download = True
        
        if (not is_sth_to_download and (invoice_site_status == "Em aberto" or invoice_site_status == "Paga")):
            downloader_logger.info("Sem arquivo para baixar")
        else:
            downloader_logger.debug(f"Linha com status {invoice_site_status}. não status para download")
    
    def _get_invoice_value(self, index: int, id_fatura: int):

        line_in_body = deepcopy(site_ele.value_column_element)
        line_in_body.set_element_index(index)

        raw_value = self._locate_element(line_in_body).text
        value = format_invoice_value(raw_value)
        updater.change_invoice_value(id_fatura, float(value))
        downloader_logger.info(f"Valor da fatura: {raw_value}")

    def _get_bar_code(self, index: int, id_fatura: int):
        
        actions_menu = deepcopy(site_ele.open_menu_button)
        actions_menu.set_element_index(index)

        bar_code_options = deepcopy(site_ele.open_bar_code_options)
        bar_code_options.set_element_index(index)

        self._click_on_element(actions_menu)

        flee_from_intercepter()
        self._click_on_element(bar_code_options)

        bar_code_element = self._locate_element(site_ele.bar_code_image, 10)
        if bar_code_element ==-1:
            downloader_logger.warning("Código de barras não disponível")
            return -1
        
        bar_code = bar_code_element.get_attribute("alt")
        updater.change_bar_code(id_fatura, bar_code)
        self._click_on_element(site_ele.close_bar_code_options)
        downloader_logger.info("Código de barras extraido")

    def _get_due_to_date(self, index: int, id_fatura: int):

        line_in_body = deepcopy(site_ele.due_to_date)
        line_in_body.set_element_index(index)

        due_to_date_text = self._locate_element(line_in_body).text
        updater.change_due_to_date(id_fatura, datetime.strptime(due_to_date_text, "%d/%m/%y"))
        downloader_logger.info(f"Data de vencimento da fatura: {due_to_date_text}")

    def _access_download_options(self, index: int):
        
        actions_menu = deepcopy(site_ele.open_menu_button)
        actions_menu.set_element_index(index)
        
        download_button = deepcopy(site_ele.open_menu_download_button)
        download_button.set_element_index(index)
        
        flee_from_intercepter()
        self._click_on_element(actions_menu)
        self._click_on_element(download_button)
        
    def _download_by_option(self, row: int, 
        kind_of_file: str, id_fatura: int):
        
        if kind_of_file == "csv":
            self._get_due_to_date(row, id_fatura)
            self._get_invoice_value(row, id_fatura)
            self._get_bar_code(row, id_fatura)
            

        self._access_download_options(row)
        
        if kind_of_file == "csv": self._click_on_element(site_ele.download_as_csv)
        if kind_of_file == "pdf": self._click_on_element(site_ele.download_as_pdf)
        
        self._click_on_element(site_ele.confirm_download_option)
        self._click_on_element(site_ele.see_download_history)
        self._click_on_element(site_ele.link_to_download)
        self._click_on_element(site_ele.close_download_history)

        updater.change_status_processment(id_fatura, "B")  
        downloader_logger.info(f"Fatura em {kind_of_file.upper()} baixada")


def format_invoice_value(valor: str):
    valor_virg = re.sub(r"[^\d,]", '', valor, 0)
    valor_ponto = valor_virg.replace(',', '.')
    return valor_ponto                
            
def flee_from_intercepter(timeout: float = 3):
    """ ### Fugir do elemento interceptador \n
    sleep para contornar elemento que aparece
    quando uma pessoa está ativa na tela
    """
    sleep(timeout)
    
def wait_table_loading(timeout: float = 4):
    """### Esperar linhas verdadeiras da tabela carregarem"""
    sleep(timeout)
    
if __name__ == "__main__":

    downloader_logger.setLevel(LoggerCreater.get_levels().info.value)
    
    fake_demand = src_folder / PATHS["test"]["demands"] / "10-2023-demand.xlsx"

    demand_manager = DemandVerifier()
    demand_manager.read_demand(fake_demand)
    demand_manager.execute()
    
    
    InvoicesDownloader(redownload = True).execute(demand_manager.demanded_list, "test")
    ApportionRunner("test").execute()