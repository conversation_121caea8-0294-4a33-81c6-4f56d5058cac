from pathlib import Path	
import sys

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 2)

from services.weblib.web_master import WebMaster
import site_ele_exemple_1
from ..logger import create_logger
from ..web_lib_utils import ResultHandle

main_exe_1 = create_logger(__name__)


web_master = WebMaster("firefox")
url = "https://www.mozilla.org/pt-BR/"

web_master.driver.get(url)


buttons = web_master.locate_all_elements(site_ele_exemple_1.firefox_browsers)

if isinstance(buttons, ResultHandle):
    main_exe_1.error("Erro ao Entrar na página de FireFox Focus")
    exit()

buttons[0].click()

web_master.click_on_element(site_ele_exemple_1.focus_firefox)

print("fim")


