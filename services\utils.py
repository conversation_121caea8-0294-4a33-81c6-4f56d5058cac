import os
import services.vars as vars
from time import perf_counter
from functools import wraps
from pathlib import Path
from math import floor
from typing import List
import sys

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 2)

from modules.logger import create_logger
from services.vars import temp_folder

if __name__ == "__main__":
    utils_logger = create_logger(__name__)
    utils_logger.setLevel(10)
else:
    utils_logger = create_logger(__name__, without_handler = True)

user = "<EMAIL>"
password = "GetnetCSC23"

STATUS_PENDENCY_FOR_CSC = "PENDENTE"


class Timer:
    """Classe que implementa métodos para realizar atividades de cronometro usando módulo time
    """
    def __init__(self, duration= 10):
        self.duration = float(duration)
        self.start = perf_counter()

    def reset(self) :
        """Reseta o contador
        """
        self.start = perf_counter()

    def increment(self,increment = 0):
        """Incrementa o contador com base em um valor

        Args:
            increment (int):Valor que irá incrementar. Defaults to 0.
        """
        self.duration += increment
    
    @property
    def not_expired(self):
        if self.duration == -1:
            return True
        return False if perf_counter() - self.start > self.duration else True  

    @property
    def expired(self):
        return self.not_expired

    @property
    def at(self):
        return perf_counter() - self.start 



def timer(func):

    @wraps(func)
    def wrapper_timer(*args, **kwargs):

        start_time = perf_counter()
        value = func(*args, **kwargs)
        end_time = perf_counter()

        run_time = end_time - start_time

        utils_logger.debug(f"Fim da {func.__name__}() em {run_time:.6f} secs")
        return value

    return wrapper_timer   




class Utils:
    """Classe que implementa métodos utilitários
    """
    def __init__(self) -> None:
        
        self.pasta_destino = vars.pasta_cancelamento
        
        self.pasta_destino = vars.pasta_desfazimento

    def clean_dir(self,tipo:str):
        """Método responsável por limpar pastas

        Args:
            tipo (str): String que classifica o destino da limpeza

        Raises:
            e: Exceção ao remover arquivo
        """
        
        if tipo == 'cancnelamento':
            self.pasta_destino = vars.pasta_cancelamento
        else:
            self.pasta_destino = vars.pasta_desfazimento
        for filename in os.listdir(self.pasta_destino):
            try:
                os.remove(r'{}//{}'.format(self.pasta_destino,filename)) 
            except Exception as e:
                raise e




class ResultHandle():

    def __init__(self, success: bool, data, error_description: str):
        """ ### Controlar o fluxo de erros

        Args:
            success (bool): Booleano que marca se a operação ocorreu bem
            data (any): Resultado esperado
            error_description (str): Descriação do erro caso houver
            wait (bool): Deve
        """
        self.success = success
        self.error_description = error_description
        self.data = data
        self.wait = False

    @property
    def failure(self):
        return not self.success

    def __str__(self):
        if self.success:
            return f'[Success]'
        else:
            return f'[Failure]: "{self.error_description}"'

    def __repr__(self):
        if self.success:
            return f"<Result success={self.success}>"
        else:
            return f'<Result success={self.success}, message="{self.error_description}">'

    @classmethod
    def Fail(cls, error, data=None):
        return cls(False, data=data, error_description=error)

    @classmethod
    def Ok(cls, data=None):
        return cls(True, data=data, error_description=None)

    @classmethod
    def Waiting(cls, error = None, data=None):
        cls_obj = cls(False, data=data, error_description=error)
        cls_obj.wait = True
        return cls_obj


def divide_in_groups(bigger_group: List, qtd_groups: int) -> List[List]:

    remanecents = len(bigger_group) % qtd_groups
    qtd_of_items_in_each_full_group = floor(len(bigger_group) / qtd_groups)

    little_groups: List[List] = []
    for full_group_index in range(qtd_groups):

        if full_group_index == 0:
            inferior_limit = 0
            superior_limit = qtd_of_items_in_each_full_group

        current_group = bigger_group[inferior_limit: superior_limit]
        little_groups.append(current_group)

        inferior_limit = superior_limit
        superior_limit = superior_limit + qtd_of_items_in_each_full_group
    
    for re_index in range(remanecents):

        if re_index == 0:
            inverted_index = (qtd_groups * qtd_of_items_in_each_full_group) - len(bigger_group)
        
        little_groups[re_index].append(bigger_group[inverted_index])

        inverted_index = inverted_index + 1
    
    return little_groups


def clear_temp_folder():

    if len(tuple(temp_folder.iterdir())) > 0:
        utils_logger.warning("Existem arquivos temporários na pasta")

    utils_logger.info("Excluíndo arquivos temporários")
    try:
        for file in temp_folder.iterdir():
            file.unlink()
    except Exception as e:
        utils_logger.critical("Erro ao exluir arquivos temporários")
        utils_logger.critical(f"Detalhes do erro: {e}")
        raise e

