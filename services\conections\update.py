from sqlite3 import Cursor
import pyodbc
import services.vars as vars
from typing import Tuple, Union


class update:
    """Classe que implementa métodos que atualizam valores no banco de dados
    """
    def __init__(self) -> None:
        driver = vars.driver
        server = vars.server
        database = vars.database
        uid = vars.uid
        pwd = vars.pwd
        mars_Connection = 'Yes'

        self.conn = pyodbc.connect(
            f'Driver={driver};Server={server};Database={database};UID={uid};PWD=*****;MARS_Connection{mars_Connection};')

        self.cursor = self.conn.cursor()


    def update_in_fluig(self,ids_fluig:int)->Tuple[int,str]:
        """Método responsável por atualizar coluna indicando que foi removido do fluig com sucesso

        Args:
            id_fluig (str): Identificador do fluig

        Returns:
            Tuple[int,str]: código de erro e mensagem 
        """
        try:

            query = f"""
            UPDATE cosmosrpa..CARTAS_DESFAZIMENTO SET IN_FLUIG = 'N'
            WHERE ID_FLUIG = {ids_fluig}
            """  
            
            # query = f"""
            # UPDATE cosmosrpa..CARTAS_DESFAZIMENTO SET IN_FLUIG = 'N'
            # WHERE ID_FLUIG in {ids_fluig}
            # """  
            self.cursor.execute(query)
            self.cursor.execute("SELECT @@IDENTITY")
            id = self.cursor.fetchone()[0]
            self.conn.commit()
            
            msg = 'Valores alterados com Sucesso'
            return id,msg       

        except:
            return -2, "Erro ao atualizar cartas de cancelamento"

    def update_sending_status(self, cod_auth:str, filial:str, status:str='Y')->Tuple[int,str]:
        """Método responsável por atualizar status do envio da carta

        Args:
            cod_auth (str): Código autorização
            filial (str): Número da filial
            status (str): status do envio da carta

        Returns:
            Tuple[int,str]: código de erro e mensagem 
        """


        try:

            query = f"""
            UPDATE cosmosrpa..CARTAS_CANCELAMENTO SET CARTA_ENVIADA = '{status}', DATA_ENVIO_CARTA = GETDATE()
            WHERE CODIGO_AUTH = '{cod_auth}' AND FILIAL = {filial}
            """  
            self.cursor.execute(query)
            self.cursor.execute("SELECT @@IDENTITY")
            id = self.cursor.fetchone()[0]
            self.conn.commit()
            msg = 'Valores alterados com Sucesso'
            return id,msg       

        except:
            return -2, "Erro ao atualizar status envio "
        
    def update_token(self, token:str, tipo:str)->Union[int,str]:
        """Método responsável por atualizar valor do token com base no tipo

        Args:
            token (str): Valor do token 
            tipo (str): identificador do tipo

        Returns:
            Union[int,str]: código de erro ou mensagem 
        """
        

        try:

            query = f"""
            "UPDATE COSMOSRPA..ACESSOS_CIELO SET TOKEN = '{token}',DATA_ATUALIZACAO = GETDATE() WHERE TIPO = '{tipo}'"
            """  
            self.cursor.execute(query)
            self.conn.commit()
            msg = 'Valores alterados com Sucesso'
            return msg       

        except:
            return -2  

    
    def update_reset_usuarios_controle(self)->Tuple[int,str]:
        """Método resetar tabela de contorle de usuarios

        Returns:
            Tuple[int,str]: código de erro e mensagem 
        """
        try:

            query = f"""
            UPDATE cosmosrpa.dbo.RPA_CONTROLE_USUARIOS_GETNET SET STATUS = 'A'
            """  
            self.cursor.execute(query)
            self.cursor.execute("SELECT @@IDENTITY")
            id = self.cursor.fetchone()[0]
            self.conn.commit()
            msg = 'Valores alterados com Sucesso'
            return id,msg       

        except:
            return -2, "Erro ao atualizar cartas de cancelamento"


    def get_not_in_use_user(self)->Tuple[int,str]:
        """

        Returns:
            Tuple[int,str]: código de erro e mensagem 
        """
        try:

            query = f"""
            update top(1) cosmosrpa.dbo.RPA_CONTROLE_USUARIOS_GETNET set STATUS = 'P', UPDATED_AT = GETDATE()
            OUTPUT INSERTED.USUARIO AS COLUNA_ATUALIZADA
            where STATUS = 'A'
            """  
            self.cursor.execute(query)
            #self.cursor.execute("SELECT @@IDENTITY")
            
            fet = self.cursor.fetchone()
            user = fet.COLUNA_ATUALIZADA

            self.conn.commit()
            
            code = 0
            return code, user       

        except Exception as error:
            print(f"Erro ao buscar usuário disponível para ser usado")
            print(f"Detalhes do erro: {error}")
            
            return -2, "sem usuarios disponiveis"                 