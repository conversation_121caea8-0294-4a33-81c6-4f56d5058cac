from typing import Tuple, Union, Iterable, List
from pathlib import Path
import sys
from datetime import datetime


def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 3)


from database import query_all_db, query_one_db, query_all_db_dw
from services.utils import ResultHandle, STATUS_PENDENCY_FOR_CSC
from models.refund_log import EstornoLog
from modules.logger import create_logger
from models.branch_id_acquirer import *




if __name__ == '__main__': 
    cod_utils_logger = create_logger('')
    cod_utils_logger.setLevel(20)
else: 
    cod_utils_logger = create_logger(__name__, without_handler = True)



def get_branch_id_in_acquirer(refund_info: EstornoLog , acquirer_code: int) -> Tuple[Union[ResultHandle, BranchIdentificationInAcquirer], EstornoLog]:

    if acquirer_code not in (102, 108):
        raise NotImplementedError(f"Não há ORM para a adquirente informada: {acquirer_code}")

    query_terminal_and_establishment = """
            select num_terminal, cod_estabelecimento, cod_filial
            from scope.clusterscopeCNF{db_cluster}.dbo.TerminalConfiguration nolock
            where 
            num_terminal <> ''
            AND cod_filial = '{branch_z_padded}'
            AND cod_rede = '{acquirer_code}'
            """

    if refund_info.codigoFilialOrigem <= 446: db_cluster = 1
    elif refund_info.codigoFilialOrigem >= 447 and refund_info.codigoFilialOrigem <= 892: db_cluster = 2
    elif refund_info.codigoFilialOrigem >= 893 and refund_info.codigoFilialOrigem <= 1338: db_cluster = 3
    elif refund_info.codigoFilialOrigem >= 1339 and refund_info.codigoFilialOrigem <= 1530: db_cluster = 4
    if refund_info.codigoFilialOrigem >= 7000: db_cluster = 5

    branch = str(refund_info.codigoFilialOrigem).zfill(4)

    query = query_terminal_and_establishment.format(db_cluster = db_cluster,
                                                    branch_z_padded = branch,
                                                    acquirer_code = acquirer_code)
    
    branch_in_acquirer = query_all_db(query)

    if len(branch_in_acquirer) == 0:
        msg = f"Não foi encontrar os dados de terminal e estabelecimento para a filial {branch}"
        refund_info.motivo = msg
        refund_info.status = STATUS_PENDENCY_FOR_CSC
        cod_utils_logger.debug(msg)
        return ResultHandle.Fail(msg), refund_info

    if len(branch_in_acquirer) > 1:
        cod_utils_logger.debug("Mais de um registro de terminal e estabelecimento encontrados")
        cod_utils_logger.debug("O primeiro será usado")


    if acquirer_code == 102:
        result = load_orm_branch_id_in_cielo_tef(branch_in_acquirer[0])
    if acquirer_code == 108:
        result = load_orm_branch_id_in_getnet_tef(branch_in_acquirer[0])

    return result, refund_info

def get_all_branches(limit: Union[int, None] = None):

    if limit is None: limite_factor = "select"
        
    else: limite_factor = f"select top {limit}"
        
    query = f"""
        {limite_factor} 
        FILI_CD_FILIAL,
        ESTA_SG_UF,
        FILI_NM_FANTASIA,
        FILI_NM_RAZSOCIAL,
        EMPR_CD_EMPRESA,
        FILI_FL_SITUACAO
        from cosmos_v14b.dbo.FILIAL nolock
        order by FILI_CD_FILIAL
        """
    
    
    all_branches = query_all_db(query)

    return all_branches


def get_branches(branches: Iterable[int]):

    query = """
        select
        FILI_CD_FILIAL,
        ESTA_SG_UF,
        FILI_NM_FANTASIA,
        FILI_NM_RAZSOCIAL,
        EMPR_CD_EMPRESA,
        FILI_FL_SITUACAO
        from cosmos_v14b.dbo.FILIAL nolock
        where FILI_CD_FILIAL in ({})
        order by FILI_CD_FILIAL
        """
    
    branches_place_holder = " ,".join("?" * len(branches))
    query = query.format(branches_place_holder)

    all_branches = query_all_db(query, *branches)

    return all_branches


# FILIAL
# CODIGO_ESTABELECIMENTO
def get_all_pdv_ec_for_active_branches(branches_limite: Union[int, None] = None, 
                                       acquirer_code: int = 108):
      
    all_branches = get_all_branches(branches_limite)
    ecs = get_ec_for_branch(all_branches, acquirer_code)
    return ecs
    

def get_pdv_ecs_for_active_branches(branches: Iterable[int], 
                                   acquirer_code: int = 108):
    
    branches_info = get_branches(branches)
    ecs = get_ec_for_branch(branches_info, acquirer_code)
    return ecs

def get_ec_for_branch(branches_info: List[Dict], acquirer_code: int = 108):

    all_establishments = []
    without_establishment = []
    for branch_info in branches_info:

        refund_info = EstornoLog(codigoFilialOrigem = branch_info["FILI_CD_FILIAL"])
        branch_acquirer, _ = get_branch_id_in_acquirer(refund_info, acquirer_code)

        if isinstance(branch_acquirer, ResultHandle):
            if branch_info["FILI_FL_SITUACAO"] not in ("D","T"):
                cod_utils_logger.warning(f"Filial {branch_info['FILI_CD_FILIAL']} ativa, está sem código de estabelecimento da getnet")
            without_establishment.append(branch_info)
            continue

        row = {"FILIAL": branch_info["FILI_CD_FILIAL"],
               "CODIGO_ESTABELECIMENTO": branch_acquirer.establishment}

        all_establishments.append(row)

    
    return all_establishments


def turn_acquirer_branch_id_into_local_branch(acquirer_branch_id: str, acquirer_code: int) -> int:

    query_terminal_and_establishment = """
            select num_terminal, cod_estabelecimento, cod_filial
            from scope.clusterscopeCNF{db_cluster}.dbo.TerminalConfiguration nolock
            where 
            num_terminal <> ''
            AND cod_estabelecimento = '{acquirer_branch_id}'
            AND cod_rede = '{acquirer_code}'
            """
    
    for db_cluster in range(1, 6, 1):

        ec = acquirer_branch_id.strip().zfill(15)
        query = query_terminal_and_establishment.format(db_cluster = db_cluster,
                                                        acquirer_branch_id = ec,
                                                        acquirer_code = acquirer_code)
        branch_macth = query_one_db(query)

        if branch_macth is None: continue
        else: break
    
    if branch_macth is None:
        cod_utils_logger.error(f"Não foi encontrado uma filial para o código de estabelecimento: {acquirer_branch_id}")
        return None
    
    return int(branch_macth["cod_filial"].lstrip("0"))


def get_all_undone_sales_by_day(sale_date: datetime):

    date_str = sale_date.strftime("%Y-%m-%d")
    query = f"""
            SELECT  
            msg.cod_filial,
            COUNT(msg.cod_filial) as quantidade
            FROM cosmosdw.scopehist.dbo.mensagem MSG (NOLOCK) 
            WHERE
            convert(date, MSG.dthr_server) = CONVERT(DATE, '{date_str}')
            AND MSG.cod_rede = '108'   
            AND MSG.situacao_mensagem in ('D', 'd', 'N')
            group by msg.cod_filial
            order by COUNT(msg.cod_filial) desc
        """
    
    undone_sales = query_all_db_dw(query)

    return undone_sales


if __name__ == "__main__":

    print("fim") 
