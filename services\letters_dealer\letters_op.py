
import PyPDF2
from datetime import datetime
import unicodedata
import pandas as pd
import base64
from typing import Union, Iterable
import services.fluig as fluig
from services.cod_esta.cod_est_utils import turn_acquirer_branch_id_into_local_branch
from services.conections.select import select
from services.conections.insert import insert
from services.conections.update import update
from modules.logger import create_logger
import services.vars as vars
from services.telegram import *
from typing import List
from pathlib import Path
import re 


if __name__ == "__main__":
    dealer_logger = create_logger("")
    dealer_logger.setLevel(10)
else:
    dealer_logger = create_logger(__name__, without_handler = True)


ConSelect = select()
ConInsert = insert()
ConUpdate= update()


def upload_letters(arqs: List[Path]):
    """Método responsável por ler pdfs, montar objetos e carregar no ambiente BPMS

    Args:
        arqs (list): Lista com os arquivos a serem lidos
    """
    
    for letter_path in arqs:
        
        with open(letter_path,'rb') as pdf_file:
        
            data = pdf_file.read()
            
            encoded = base64.b64encode(data)
            encoded_utf8 = encoded.decode('utf-8')
            read_pdf = PyPDF2.PdfReader(pdf_file)
            page = read_pdf.pages[0]
            page_content = page.extract_text()
            listatext = page_content.split('\n')
            
            DATA_VENDA = listatext[3].replace('Data da Transação: ','').split('/')
            HORA_VENDA = listatext[5].replace('Horário: ','').split(':')
        

        obj_carta = {
            'DATA_VENDA' : f'{DATA_VENDA[2]}-{DATA_VENDA[1]}-{DATA_VENDA[0]} {HORA_VENDA[0]}:{HORA_VENDA[1]}:{HORA_VENDA[2]}',
            'ESTABELECIMENTO': listatext[16].replace('Código de Estabelecimento:',''),
            'VALOR_VENDA' : listatext[4].replace('Valor da Transação: R$ ','').replace('.','').replace(',','.'),
            'NUMERO_CARTAO' : listatext[14].replace('Importante!Cartão: ',''),
            'NSU' : listatext[15].replace('NSU: ','')
        }

        branch = turn_acquirer_branch_id_into_local_branch(obj_carta['ESTABELECIMENTO'], acquirer_code=108)
          
        if branch is not None:
            obj_carta['FILIAL'] = branch
        else:
            dealer_logger.error(f"Erro ao encontra filial para o cód. est. {obj_carta['ESTABELECIMENTO']}")
            continue 
            

        carta = ConSelect.select_undo_letter(obj_carta)
        
        if carta[0] == 0:
            dealer_logger.info(f'Carta já existe no Fluig Cód. est.:{obj_carta["ESTABELECIMENTO"]} da filial {obj_carta["FILIAL"]}')
            continue

        ID_PASTA = ConSelect.select_fluig_id(obj_carta['FILIAL'])
        
        if ID_PASTA == -1:
            ID_PASTA = fluig.create_folder(obj_carta['FILIAL'])
            id,_ = ConInsert.insert_fluig_id(obj_carta['FILIAL'], ID_PASTA)
            
        obj_carta['ID_FLUIG'] = fluig.upload_document(f"{obj_carta['NSU']}_{DATA_VENDA[2]}{DATA_VENDA[1]}{DATA_VENDA[0]}.pdf", encoded_utf8, ID_PASTA)
        
        
        id, _ = ConInsert.insert_undo_letter(obj_carta)

        if id == 0:
            #sucesso
            dealer_logger.info(f"Carta do cód. est. {obj_carta['ESTABELECIMENTO']} da filial {obj_carta['FILIAL']} e NSU: {obj_carta['NSU']} foi carregada")
        else:
            #erro 
            dealer_logger.error(f"Falha ao carregar carta do cód. est. {obj_carta['ESTABELECIMENTO']} da filial {obj_carta['FILIAL']} e NSU: {obj_carta['NSU']}")   
             

def delete_massive_fluig(ids):
    
    """Método responsável por verificar se existe casos para remoção, 
    e executar o processo caso necessário
    """

    if (ids == -1 or ids == -2):
        print('sem casoso para deletar')
        telegram_bot_sendtext('sem casoso para deletar')
    else:    
        
        delete,msg = fluig.delete_documents(ids)
        if delete == -1:
            print(f'erro no delete:{msg}')
            telegram_bot_sendtext(f'erro no delete:{msg}')
        else:
           
            ids_docs = tuple(tupla[0] for tupla in ids)
        
            ConUpdate.update_in_fluig(ids_docs)
    

def delete_fluig(ids: Union[int, Iterable], date_to_search: datetime):
    """Método responsável por verificar se existe casos para remoção, 
    e executar o processo caso necessário
    """

    if (ids == -1 or ids == -2):
        print('sem casos para deletar')
        telegram_bot_sendtext('sem casos para deletar')
    else:    
        for i in ids:
            print(f"Id do arquivo {i[0]} do dia {date_to_search}")
            delete,msg = fluig.delete_document(i[0])
            if delete == -1:
                print(f'Erro ao deletar no fluig:{msg}; dia: {date_to_search}')
                telegram_bot_sendtext(f'Erro ao deletar no fluig:{msg}; dia: {date_to_search}')
            elif delete == -2:
                print (f'erro no detroy:{msg}; dia: {date_to_search}')
                telegram_bot_sendtext(f'erro no detroy:{msg}; dia: {date_to_search}')
            else:
                ConUpdate.update_in_fluig(i[0])


class PdfReaderLetter:
    """Classe que implementa métodos para ler cartas em PDF
    """
    def __init__(self) -> None:
        self.obj_carta ={
            'DATA_VENDA' : None,
            'VALOR_VENDA' : None,
            'DATA_CANCELAMENTO' : None,
            'VALOR_CANCELAMENTO' : None,
            'NUMERO_CARTAO' : None,
            'ESTABELECIMENTO' : None,
            'CODIGO_AUTH' : None,
            'PLANO_VENDA' : None,
            'TIPO' : 'Carta de Cancelamento',
            'FILIAL': None
        }

    def extract_letter_data(self, file:str, type:str)->dict:
        """Método responsável por extrair texto de um arquivo

        Args:
            file (str): Caminho do arquivo a ser extraído
            type (str): Categoria do arquivo a ser extraído

        Returns:
            dict: Dicionário com os dados obtidos do arquivo 
        """
        pdf_file = open(file,'rb')
        read_pdf = PyPDF2.PdfFileReader(pdf_file)
        page = read_pdf.getPage(0)
        page_content = page.extractText()


        if type == 'cielo':
            nome_arq= file.split('\\')[-1]
            filial = nome_arq.split('_')[0]
            cod_estab = nome_arq.split('_')[1].replace('.pdf', '')
            self.__get_data_cielo(page_content, cod_estab, filial)
        else:
            self.__get_data_getnet(page_content)
            self.obj_carta['FILIAL'] = ConSelect.select_ECO_branch(self.obj_carta['ESTABELECIMENTO'])

            if self.obj_carta['FILIAL'] == -1:
                self.obj_carta['FILIAL'] = ConSelect.select_TEF_branch(self.obj_carta['ESTABELECIMENTO'])
                if self.obj_carta['FILIAL'] == -1:
                    print(f'estabelecimento não encontrado {self.obj_carta["ESTABELECIMENTO"]}')
                    telegram_bot_sendtext(f'estabelecimento não encontrado {self.obj_carta["ESTABELECIMENTO"]}')
                    self.obj_carta['FILIAL'] = None

        print(self.obj_carta)
        
        return self.obj_carta


    def __get_data_cielo(self, text:str, estabelecimento:str, filial:str)->dict:
        """Método responsável por obter dados do documento da Cielo

        Args:
            text (str): Texto obtido do documento
            estabelecimento (str): código do estabelecimento
            filial (str): Número da filial

        Returns:
            dict: dicionário com os dados sobre o documento
        """
        try:
            print(text)
            listatext = text.split('\n')

            DATA_VENDA = self.__get_data_text_cielo(listatext,'Data da venda:').replace('Data da venda:  ','').split('/')
            DATA_CANCELAMENTO = self.__get_data_text_cielo(listatext,'Data de efetivação').replace('Data de efetivação:  ','').split('/')
            
            self.obj_carta= {
                    'DATA_VENDA' : f'{DATA_VENDA[2]}-{DATA_VENDA[1]}-{DATA_VENDA[0]}',
                    'VALOR_VENDA' : unicodedata.normalize('NFKD', re.sub('Valor[ ]*da[ ]*venda:[ ]*', '', self.__get_data_text_cielo(listatext,'Valor da venda:')).replace('.','').replace(',','.')).replace('R$','').strip(), #listatext[8].replace('Valor da venda:  R$ ','').replace('.','').replace(',','.'),
                    'DATA_CANCELAMENTO' : f'{DATA_CANCELAMENTO[2]}-{DATA_CANCELAMENTO[1]}-{DATA_CANCELAMENTO[0]}',
                    'VALOR_CANCELAMENTO' : unicodedata.normalize('NFKD', re.sub('Valor[ ]*do[ ]*cancelamento:[ ]*', '', self.__get_data_text_cielo(listatext,'Valor do cancelamento:')).replace('.','').replace(',','.')).replace('- R$','').strip(),#listatext[9].replace('Valor do cancelamento:  - R$ ','').replace('.','').replace(',','.'),
                    'NUMERO_CARTAO' : ('X'*12)+self.__get_data_text_cielo(listatext,'Nº Cartão (4 últimos dígitos):').replace('Nº Cartão (4 últimos dígitos):  ',''),
                    'ESTABELECIMENTO' : estabelecimento,
                    'CODIGO_AUTH' : self.__get_data_text_cielo(listatext,'Código de autorização:').replace('Código de autorização:  ',''),
                    'PLANO_VENDA' : None,
                    'TIPO' : 'Carta de Cancelamento',
                    'FILIAL':filial
                }
            

        except:
            return None 


    def __get_data_text_cielo(self,text:list,search:str)->str:
        """Método que busca linha de acordo com uma chave de busca

        Args:
            text (list): Lista de textos 
            search (str): Texto usado para buscar

        Returns:
            str: retorna a string encontrada
        """
        
        for i in text:
            if search in i:
                return i
            
            else: continue    



    def __get_data_getnet(self, text:str)->dict:
        """Método responsável por estruturar objeto a partir do texto obtido do documento getnet

        Args:
            text (str): Texto obtido do documento getnet

        Returns:
            dict: dicionário com os dados estruturados
        """
        try:
            listatext = text.split('\n')

            DATA_VENDA = listatext[6].replace('Data da Venda: ','').split('/')
            DATA_CANCELAMENTO = listatext[8].replace('Data do Cancelamento: ','').split('/')
            
            self.obj_carta= {
                    'DATA_VENDA' : f'{DATA_VENDA[2]}-{DATA_VENDA[1]}-{DATA_VENDA[0]}',
                    'VALOR_VENDA' : listatext[7].replace('Valor da Venda: R$ ','').replace('.','').replace(',','.'),
                    'DATA_CANCELAMENTO' : f'{DATA_CANCELAMENTO[2]}-{DATA_CANCELAMENTO[1]}-{DATA_CANCELAMENTO[0]}',
                    'VALOR_CANCELAMENTO' : listatext[9].replace('Valor do Cancelamento: R$ ','').replace('.','').replace(',','.'),
                    'NUMERO_CARTAO' : listatext[10].replace('Número do Cartão: ',''),
                    'ESTABELECIMENTO' : listatext[11].replace('Estabelecimento: ',''),
                    'CODIGO_AUTH' : listatext[12].replace('Código de Autorização: ',''),
                    'PLANO_VENDA' : listatext[13].replace('Plano de Venda: ',''),
                    'TIPO' : 'Carta de Cancelamento',
                    'FILIAL': None
                }
        except:
            return None
