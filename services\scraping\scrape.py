
from typing import Tu<PERSON>, List, Dict, Union
import asyncio
from copy import deepcopy
from datetime import datetime, timedelta
from selenium.webdriver.firefox.options import Options
from selenium.webdriver.firefox.firefox_profile import FirefoxProfile
import sys
from pathlib import Path

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 3)


from services.cod_esta.cod_est_utils import get_all_pdv_ec_for_active_branches, get_pdv_ecs_for_active_branches
from services.vars import temp_folder, GETNET_LOGIN_SITE, STATUS_LETTER_ENDPOINT
from services.credentials import BotcityManager
from services.utils import timer, ResultHandle
from services.conections.update import update 
from modules.weblib.web_master import WebMaster, WebLibError
from modules.logger import create_logger
import models.getnet_site_elements as getnet_site_elements

if __name__ == "__main__":
    scrape_logger = create_logger("",file_output="test_scrape.log")
    scrape_logger.setLevel(10)
else:
    scrape_logger = create_logger(__name__, without_handler = True)

update_cosmos = update()

def generate_driver_conf() -> Tuple[FirefoxProfile, Options]:

    profile = FirefoxProfile()
    profile.set_preference("general.useragent.override", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:126.0) Gecko/20100101 Firefox/126.0")
    profile.set_preference("browser.download.folderList", 2)  # 0: Desktop, 1: Downloads, 2: Pasta personalizada
    profile.set_preference("browser.download.dir", temp_folder.__str__())
    profile.set_preference("browser.helperApps.neverAsk.saveToDisk", "application/octet-stream,application/pdf")  # Evitar popup de confirmação de download

    options = Options()
    options.headless = False  

    return profile, options


def getnet_user_credentials() -> Tuple[str, str]:

    not_in_use_user = update_cosmos.get_not_in_use_user()
    if not_in_use_user[0] != 0:
        return -1, not_in_use_user[1]

    code = not_in_use_user[1].split('_')

    botcity_manager = BotcityManager()
    user_current = botcity_manager.get_credentials('USUARIOS_GETNET','USUARIO_'+code[1])
    password_current = botcity_manager.get_credentials('USUARIOS_GETNET','SENHA_'+code[1])

    if user_current[1] == 0 and password_current[1] == 0:
        user_current = user_current[0]
        password_current = password_current[0]
        return user_current, password_current
    else:
        scrape_logger.critical("Erro ao obter as credênciais dos usuários atuais")
        exit()

async def avoid_animation(timeout: float = 5):
    await asyncio.sleep(timeout)



def login(web_master: WebMaster, worker_id: Union[int, None]):

    try:
        scrape_logger.info("Entrando na tela de login da getnet")
        web_master.driver.get(GETNET_LOGIN_SITE)
        web_master.driver.maximize_window()
        web_master.current_screen = getnet_site_elements.LOGIN_SCREEN

        web_master.click_on_element(getnet_site_elements.allow_cookies_btn)

        if worker_id is not None:
            web_master.driver.execute_script(f'document.title = "worker {worker_id}"')

        web_master.click_on_element(getnet_site_elements.access_credential_menu_btn)


        user_current, password_current = getnet_user_credentials()   

        scrape_logger.info(f"Usando o usuário para logar no site: {user_current}")
        web_master.write_in_field(user_current, getnet_site_elements.user_field)
        web_master.write_in_field(password_current, getnet_site_elements.password_field)

        access_btn = web_master.locate_element(getnet_site_elements.access_account_btn)
        asyncio.run(avoid_animation())
        web_master.driver.execute_script("arguments[0].click();", access_btn)
        asyncio.run(avoid_animation(45))

    except WebLibError as e:
        scrape_logger.critical("Erro ao fazer login no site")
        raise e
    
    except Exception as e:
        scrape_logger.critical("Erro ao fazer login no site")
        scrape_logger.critical(f"Detalhes do erro: {e}")
        raise e


def select_ec(web_master: WebMaster, ec: Dict, sale_date: datetime):

    try:
        scrape_logger.info(f"Pesquisando o cód. est. {ec['CODIGO_ESTABELECIMENTO']} referente a filial {ec['FILIAL']} do dia {sale_date.strftime('%d/%m/%Y')}")

        web_master.click_on_element(getnet_site_elements.open_establisment_selector)
        web_master.write_in_field(ec["CODIGO_ESTABELECIMENTO"], getnet_site_elements.establisment_field)

        web_master.click_on_element(getnet_site_elements.search_establisment_btn)

        found_establisment = deepcopy(getnet_site_elements.found_establisment_template)
        found_establisment.fill_kwargs_in_identifier(ec = ec['CODIGO_ESTABELECIMENTO'])
        web_master.click_on_element(found_establisment)
        scrape_logger.info("Filial selecionada")

        sale_date_str = sale_date.strftime("%d%m%Y")
        web_master.write_in_field(sale_date_str, getnet_site_elements.sale_date) 

        search_letters_btn = web_master.locate_element(getnet_site_elements.search_letters_btn)
        web_master.driver.execute_script("arguments[0].click();", search_letters_btn)

    except WebLibError as e:
        scrape_logger.error("Erro na etapa seleção do código de estabelecimento")
        raise e
    
    except Exception as e:
        scrape_logger.error("Erro na etapa seleção do código de estabelecimento")
        scrape_logger.error(f"Detalhes do erro: {e}")
        raise e

def download_letter(web_master: WebMaster):

    try:
        try:
            download_btns = web_master.locate_all_elements(getnet_site_elements.download_lettter_btn)
        except Exception as e:
            scrape_logger.info("Cód. est. sem cartas de desfazimento para a data da venda")
            raise e
        
        scrape_logger.info("Iniciando Download das cartas disponíveis")
        main_tab = web_master.driver.current_window_handle
        old_tabs = web_master.driver.window_handles

        scrape_logger.info(f"Quantidade de Cartas disponíveis: {len(download_btns)}")
        for btn in download_btns:
            
            web_master.driver.execute_script("arguments[0].click();", btn)
            track = web_master.wait_change_in_tabs_quant()

            if track == -1: 
                web_master.click_on_element(getnet_site_elements.download_erro_btn)
                continue
            
            current_tab = web_master.get_the_new_tab_hash(old_tabs)
            web_master.change_tab(current_tab)
            web_master.driver.close()
            web_master.change_tab(main_tab)
        
        scrape_logger.info("Cartas baixadas")
    
    except WebLibError as e:

        scrape_logger.error("Erro na etapa de download das cartas")
        without_letters = web_master.locate_element(getnet_site_elements.without_letters_evidence, 
                                                    raise_approach=False)
        
        if isinstance(without_letters, ResultHandle):
            scrape_logger.critical("Erro não conhecido")
        else:
            scrape_logger.warning("Cód. Est. não possui cartas para a data da venda")

        raise e
    
    except Exception as e:
        scrape_logger.error("Erro na etapa de download das cartas")
        scrape_logger.error(f"Detalhes do erro: {e}")
        raise e
        

def scrape(ecs: List[Dict], 
           sale_date: datetime = (datetime.today() - timedelta(days=1)),
           worker_id: int = None):


    profile, options = generate_driver_conf()

    web_master = WebMaster(driver_brand="firefox",
                        options=options,
                        profile=profile)

    

    login(web_master, worker_id)
    scrape_logger.info("Entrando na tela de carta status")
    web_master.driver.get(STATUS_LETTER_ENDPOINT)
    is_to_retry_login(web_master, worker_id)
            
    web_master.current_screen = getnet_site_elements.STATUS_LETTER_SCREEN
    for ec in ecs: 
        
        try:
            select_ec(web_master, ec, sale_date)
            download_letter(web_master)
        except:
            scrape_logger.info("Indo para proxima filial")
            web_master.driver.refresh()
            continue
    
    web_master.driver.quit()

def is_to_retry_login(web_master: WebMaster, worker_id: int, max_attempts: int = 3):

    is_in_status_letter = web_master.wait_change_in_url(STATUS_LETTER_ENDPOINT)
    attempts = 0
    while (attempts < max_attempts and not is_in_status_letter):
        
        attempts = attempts + 1
        scrape_logger.error(f"Erro no login. Tentando mais uma vez. Tentativa: {attempts}")

        login(web_master, worker_id)
        web_master.driver.get(STATUS_LETTER_ENDPOINT)
        is_in_status_letter = web_master.wait_change_in_url(STATUS_LETTER_ENDPOINT)
    
    if not is_in_status_letter:
        scrape_logger.critical("O máximo de tentativas de login foi excedido")
        raise Exception("Não foi possível fazer login")
    


    


if __name__ == "__main__": # 

    botcity_manager = BotcityManager()
    users = []
    for i in range(1, 11, 1):
        
        user_current = botcity_manager.get_credentials('USUARIOS_GETNET','USUARIO_'+ str(i))
        password_current = botcity_manager.get_credentials('USUARIOS_GETNET','SENHA_'+ str(i))

        user_info = {
            "name": user_current,
            "password": password_current
        }
        users.append(user_info)

    from services.utils import clear_temp_folder

    #Usuário 9
    #Usuário 6
    #Usuário 3
    scrape_logger.setLevel(10)
    clear_temp_folder()

    update_cosmos = update()
    tracker, msg = update_cosmos.update_reset_usuarios_controle()
    if tracker != None:
        scrape_logger.critical(msg)
        exit()
    
    #ecs = get_pdv_ecs_for_active_branches((46, 47, 49))
    ecs = get_all_pdv_ec_for_active_branches(10)
    scrape(ecs)

    print("fim")










