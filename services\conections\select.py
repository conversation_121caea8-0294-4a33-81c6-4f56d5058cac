import pyodbc
import services.vars as vars
from typing import Union

class select:
    """Classe que implementa métodos que consultam no banco de dados
    """

    def __init__(self) -> None:
        """Construtor da classe
        """
        driver = vars.driver
        server = vars.server
        database = vars.database
        uid = vars.uid
        pwd = vars.pwd
        mars_Connection = 'Yes'

        self.conn = pyodbc.connect(
            f'Driver={driver};Server={server};Database={database};UID={uid};PWD=*****;MARS_Connection{mars_Connection};')

        self.cursor = self.conn.cursor()    

    def select_fluig_id(self,filial:str)->int:
        """Método responsável por consultar Identificado da filial no fluig

        Args:
            filial (str): Número da filial

        Returns:
            int: Identificador do fluig 
        """
        try:

            query = f"""
            SELECT ID_FLUIG FROM cosmosrpa..PASTAS_FLUIG
            WHERE FILIAL = {filial}
            """  
            self.cursor.execute(query)
            dataResp = self.cursor.fetchall()
            if dataResp != []:
                id = (dataResp[0][0])
                return id
            else: 
                return -1      

        except:           
            return -2    

    def select_letter(self,obj_carta:dict)->int:
        """Método responsável por consultar identificador da carta de cancelamento 

        Args:
            obj_carta (dict): Dicionário com valores referentes a carta de cancelamento

        Returns:
            int: Identificador da carta
        """
        try:

            query = f"""
            SELECT ID_FLUIG FROM cosmosrpa..CARTAS_CANCELAMENTO
            WHERE FILIAL = {obj_carta['FILIAL']} 
            AND CODIGO_AUTH = '{obj_carta['CODIGO_AUTH']}'
            AND DATA_VENDA = '{obj_carta['DATA_VENDA']}'
            """  
            self.cursor.execute(query)
            dataResp = self.cursor.fetchall()
            if dataResp != []:
                id = (dataResp[0][0])
                return id
            else: 
                return -1      

        except:           
            return -2   

    def select_undo_letter(self,obj_carta:object):
        """Método responsável por consultar identificador da carta de desfazimento 

        Args:
            obj_carta (dict): Dicionário com valores referentes a carta de desfazimento

        Returns:
            int: Identificador da carta 
        """
        try:

            query = f"""
            SELECT ID_FLUIG FROM cosmosrpa..CARTAS_DESFAZIMENTO
            WHERE FILIAL = {obj_carta['FILIAL']} 
            AND NSU = '{obj_carta['NSU']}'
            AND DATA_VENDA = '{obj_carta['DATA_VENDA']}'
            """  
            self.cursor.execute(query)
            dataResp = self.cursor.fetchall()
            if dataResp != []:
                id = (dataResp[0][0])
                return 0,id
            else: 
                return 1,'Carta não existe na tabela'      

        except:           
            return -2,'erro na consulta cartas desfazimento'        

    def select_ECO_branch(self,CODIGO_ESTABELECIMENTO:int):
        """Método responsável por consultar filial a partir do código do estabelecimento na tabela ESTABELECIMENTO_ECOMMERCE

        Args:
            CODIGO_ESTABELECIMENTO (int): Número do código do estabelecimento

        Returns:
            int: Número da filial
        """
        try:

            query = f"""
            SELECT FILIAL FROM cosmosrpa..ESTABELECIMENTO_ECOMMERCE
            WHERE CODIGO_ESTABELECIMENTO = {CODIGO_ESTABELECIMENTO}
            AND ADQUIRENTE = 'Getnet'  
            """  
            self.cursor.execute(query)
            dataResp = self.cursor.fetchall()
            if dataResp != []:
                id = (dataResp[0][0])
                return id
            else: 
                return -1      

        except:           
            return -2   

    def select_TEF_branch(self,CODIGO_ESTABELECIMENTO:int):
        """Método responsável por consultar filial a partir do código do estabelecimento na tabela ESTABELECIMENTO_PDV

        Args:
            CODIGO_ESTABELECIMENTO (int): Número do código do estabelecimento

        Returns:
            int: Número da filial
        """
        try:

            query = f"""
            SELECT FILIAL FROM cosmosrpa..ESTABELECIMENTO_PDV
            WHERE CODIGO_ESTABELECIMENTO = {CODIGO_ESTABELECIMENTO}
            AND ADQUIRENTE = 'Getnet' 
            """  
            self.cursor.execute(query)
            dataResp = self.cursor.fetchall()
            if dataResp != []:
                id = (dataResp[0][0])
                return 0,id
            else: 
                return -1,'erro'      

        except:           
            return -2,'erro'

    def select_docs_delete(self,DATA_VENDA:str):
        """Método responsável por consultar identificado do fluig a partir da data de cancelamento

        Args:
            DATA_CANCELAMENTO (int): data do cancelamento

        Returns:
            int: Número do identificado do fluig
        """
        try:

            query = f"""
            SELECT top 500 ID_FLUIG, FILIAL FROM cosmosrpa..CARTAS_DESFAZIMENTO
            WHERE DATA_VENDA < '{DATA_VENDA}'
            AND IN_FLUIG = 'Y'
            """  
            
            # query = f"""SELECT top 2000 ID_FLUIG, FILIAL,
            #             (SELECT ID_FLUIG 
            #                 FROM PASTAS_FLUIG 
            #                 WHERE FILIAL = CARTAS_DESFAZIMENTO.FILIAL) AS PASTA_FLUIG
            #         FROM CARTAS_DESFAZIMENTO 
            #         WHERE convert(date, DATA_VENDA) < '{DATA_VENDA}'
            #         AND IN_FLUIG = 'Y'"""
            self.cursor.execute(query)
            dataResp = self.cursor.fetchall()
            
            if dataResp != []: 
                return dataResp
            else: 
                return -1      

        except Exception as e:           
            return -2                         

    def select_letter_pending_sending(self)->Union[int, dict]:
        """Método responsável por consultar as cartas de cancelamento que estão pendente envio


        Returns:
            Union[int, dict]: Código de erro ou dicionário com valores das cartas
        """


        cursor = self.conn.cursor()
        try:

            query = f"""
            SELECT * FROM cosmosrpa..CARTAS_CANCELAMENTO
            WHERE IN_FLUIG = 'Y' and CARTA_ENVIADA IS NULL
            """  
            cursor.execute(query)
            
            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))

            return results

        except:           
            return -2    


    def pending_letters_cielo_cancellation(self)->Union[int, dict]:
        """Método responsável por consultar as cartas de cancelamento que estão pendente cancelamento da cielo


        Returns:
            Union[int, dict]: Código de erro ou dicionário com valores das cartas
        """
 

        cursor = self.conn.cursor()
        try:

            query = f"""
            SELECT * FROM cosmosrpa..ESTORNO_VENDA_LOG e
            WHERE NOT EXISTS (SELECT * FROM cosmosrpa..CARTAS_CANCELAMENTO r WHERE e.CODIGO_AUTORIZAÇÂO = r.CODIGO_AUTH AND e.FILIAL_ORIGEM = r.FILIAL) 
            AND status = 'FINALIZADO' AND MOTIVO = 'REALIZADO' AND protocolo_cielo is not NULL
            """  
            cursor.execute(query)
            #dataResp = self.cursor.fetchall()
            
            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))

            return results

        except Exception as e:
            print(e)           
            return -2    

    
    
    def get_cielo_authorization(self, empresa='pmenos')->Union[int, str]:
        """Método responsável por consultar token da api cielo 


        Returns:
            Union[int, str]: Código de erro ou token da api cielo 

        """
       

        cursor = self.conn.cursor()
        try:
            if empresa == 'pmenos':
                query = f"""
                SELECT TOKEN FROM cosmosrpa..ACESSOS_CIELO WHERE TIPO = 'Access_Token_Cielo'
                """  
            else:
                query = "SELECT TOKEN FROM COSMOSRPA..ACESSOS_CIELO WHERE TIPO = 'Access_Token_Cielo_Ext' "

            cursor.execute(query)
            
            
            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))

            return results[0]['TOKEN']

        except Exception as e:
            print(e)           
            return -2 
    
    
    def get_establishment_code(self, tipo_pos, filial)->str:
        """Método responsável por consultar código estabelecimento, dependendo se é POS ou não, da tabela específica

        Args:
            tipo_pos (Str): Identificador se é POS ou não
            filial (str): Número da filial
            

        Returns:
            str: Número do código estabelecimento
        """

        if tipo_pos == 'S':
            sql = f"""SELECT CODIGO_ESTABELECIMENTO FROM cosmosrpa..ESTABELECIMENTO_ECOMMERCE WHERE FILIAL = '{filial}' AND ADQUIRENTE = 'Cielo' """
        else: 
            sql = f"""SELECT CODIGO_ESTABELECIMENTO FROM cosmosrpa..ESTABELECIMENTO_PDV WHERE FILIAL = '{filial}' AND ADQUIRENTE = 'Cielo' """
            
        cursor = self.conn.cursor()
        try:
            cursor.execute(sql)
            
            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))

            if results == []:
                return None

            return results[0]['CODIGO_ESTABELECIMENTO']

        except:           
            return -2 
    

    def select_client_data(self, filial:str, cod_aut:str)->Union[int,dict]:
        """Método responsável por consultar dados sobre o cliente envolvido no estorno

        Args:
            cod_aut (str): número do código de autorização
            filial (str): Número da filial
            

        Returns:
            Union[int,dict]: Código de erro ou dicionário com os dados do cliente
        """
        

        cursor = self.conn.cursor()
        query = f"""
            SELECT * FROM cosmosrpa..ESTORNO_VENDA_LOG
            WHERE FILIAL_ORIGEM = {filial} AND CODIGO_AUTORIZAÇÂO = '{cod_aut}'
            """  
        try:

            cursor.execute(query)
            
            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))

            if results == []:
                return None
            
            return results[0]

        except Exception as e:
            print(query)
            print(f"Erro ao consultar dados cliente. Detahe: {e}")           
            return -2    

       


    def select_establishment(self)->Union[int,dict]:
        """Método responsável por consultar o código do estabelecimento

        Returns:
            Union[int,dict]: Código de erro ou dicionário com os dados do cliente
        """
        
        cursor = self.conn.cursor()
        query = f"""
            select FILIAL,CODIGO_ESTABELECIMENTO from ESTABELECIMENTO_PDV where ADQUIRENTE = 'GETNET'
            AND CODIGO_ESTABELECIMENTO <> 0
            AND FILIAL < 7000
            ORDER BY FILIAL 
            """ 
         
        try:

            cursor.execute(query)
            #dataResp = self.cursor.fetchall()
            
            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))

            if results == []:
                return None
            
            return results

        except Exception as e:
            print(query)
            print(f"Erro ao consultar dados cliente. Detahe: {e}")           
            return -2    
       
