import requests
import json
import time
# import schedule

botname = "rpacartasbot"
bot_token = '6660914459:AAHakmk_9hLVfth6h-IBTzIWd6GLtYSirsU'
chat_id = '-4190855996'



def telegram_bot_sendtext(msg:str):
    """Método responsável por  enviar mensagem via API do telegram

    Args:
        msg (str): Mensagem a ser envia via telegram
    """
    print(msg)
    try:
        data = {"chat_id": chat_id, "text": ('Cartas de Desfazimento:'+msg)}
        url = "https://api.telegram.org/bot{}/sendMessage".format(bot_token)
        requests.post(url, data)
    except Exception as e:
        print(f"Erro no sendMessage: {e}")