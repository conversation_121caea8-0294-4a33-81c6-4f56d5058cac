from typing import Dict, Union
from pydantic import BaseModel, model_validator
from decimal import Decimal

__all__ = ["PaymentMethodData", 
           "load_payment_method_data_orm"]

class PaymentMethodData(BaseModel):
    refund_id: int
    branch: int
    total_invoice_value: Decimal
    value_to_refund: Decimal
    payment_method_value: Decimal
    nsu_tef: Union[int, None]
    acquirer_code: Union[int, None]
    invoice_number: str
    external_nsu: Union[str, None]

    @model_validator(mode="after")
    def format_fields(self):

        self.total_invoice_value = float(self.total_invoice_value)
        self.value_to_refund = float(self.value_to_refund)
        self.payment_method_value = float(self.payment_method_value)

        if self.invoice_number is not None: 
            self.invoice_number = self.invoice_number.strip()

        if self.external_nsu is not None: 
            self.external_nsu = self.external_nsu.strip()

        return self

        

def load_payment_method_data_orm(data: Dict) -> PaymentMethodData:
    
    payment_data = PaymentMethodData(
        refund_id=data['ID_ESTORNO'],
        branch=data['FILIAL_ORIGEM'],
        total_invoice_value=data['VALOR_CUPOM'],
        value_to_refund=data['VALOR_ESTORNO'],
        payment_method_value=data['VALOR_FINALIZADOR'],
        nsu_tef=data['FLVD_NR_NSU_TEF'],
        acquirer_code=data['REDE_SCOPE'],
        invoice_number=data['NUMERO_COO'],
        external_nsu=data['NSU_AUTORIZADORA_TEF']
    )

    return payment_data