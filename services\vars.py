import sys
from pathlib import Path

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 2)


temp_folder = src_folder / r"arquivos\temp"
log_folder = src_folder / r"arquivos\logs"
GETNET_LOGIN_SITE = 'https://minhaconta.getnet.com.br/login'
STATUS_LETTER_ENDPOINT = 'https://minhaconta.getnet.com.br/financeiro/documentos/carta-status'
pasta_cancelamento = temp_folder.__str__()
pasta_desfazimento = temp_folder.__str__()

user = "<EMAIL>"
password = "GetnetCSC23"

#usernamefluig = 'pedrofreitas'
#passwordfluig = 'EPM@2022'
#dadfolderfluig = 26248
#publisheridfluig = 115102

usernamefluig = '<EMAIL>'
passwordfluig = 'P@rTaLD02CoM1t32.2021'
dadfolderfluig = 26701
publisheridfluig = 'admin'

driver = "{SQL Server}"
server = "COSMOS"
database = "CosmosRPA"
uid = "usercosmos"
pwd = "KOS1520MO"

#pasta = "E://RPA//Homologation//RPA_CARTA_DEVOLUCAO//"

#ecmfolder = "https://devpmenos.fluig.cloudtotvs.com.br/webdesk/ECMFolderService"
#cmdocument = "https://devpmenos.fluig.cloudtotvs.com.br/webdesk/ECMDocumentService"
ecmfolder = "https://pmenos.fluig.cloudtotvs.com.br/webdesk/ECMFolderService"
ecmdocument = "https://pmenos.fluig.cloudtotvs.com.br/webdesk/ECMDocumentService"

ECSEMPRESAS = ['11471658','4467230']

createfolderfluig = """
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ws="http://ws.dm.ecm.technology.totvs.com/">
                    <soapenv:Header/>
                    <soapenv:Body>
                        <ws:createSimpleFolder>
                            <username>{}</username>
                            <password>{}</password>
                            <companyId>1</companyId>
                            <parentDocumentId>{}</parentDocumentId>
                            <publisherId>{}</publisherId>
                            <documentDescription>{}</documentDescription>
                        </ws:createSimpleFolder>
                    </soapenv:Body>
                </soapenv:Envelope>
              """

uploaddocumentfluig = """
                    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ws="http://ws.dm.ecm.technology.totvs.com/">
                        <soapenv:Header/>
                        <soapenv:Body>
                            <ws:createSimpleDocument>
                                <username>{}</username>
                                <password>{}</password>
                                <companyId>1</companyId>
                                <parentDocumentId>{}</parentDocumentId>
                                <publisherId>{}</publisherId>
                                <documentDescription>{}</documentDescription>
                                <Attachments>
                                    <item>
                                    <fileName>{}</fileName>
                                    <filecontent>{}</filecontent>
                                    <principal>true</principal>
                                    </item>
                                </Attachments>
                            </ws:createSimpleDocument>
                        </soapenv:Body>
                    </soapenv:Envelope>
                    """

deletefluig = """
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ws="http://ws.dm.ecm.technology.totvs.com/">
                <soapenv:Header/>
                <soapenv:Body>
                    <ws:{}>
                        <user>{}</user>
                        <password>{}</password>
                        <companyId>1</companyId>
                        <documentId>{}</documentId>
                        <colleagueId>{}</colleagueId>
                    </ws:{}>
                </soapenv:Body>
            </soapenv:Envelope>   
            """        

deleteMassiveFluig = {
  "docsToDelete": [],
  "metadataFormsToDelete": []
}                         
