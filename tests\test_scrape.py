from pathlib import Path
import sys
from datetime import datetime

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 2)

from services.scraping.scrape import scrape
from services.utils import clear_temp_folder
from modules.logger import create_logger


logger = create_logger("", file_output="test_scrape.log")
logger.setLevel(20) 


ec_list = [
    {"FILIAL": 840, "CODIGO_ESTABELECIMENTO": "1445439"}
]

scrape(ec_list, datetime(2025, 6, 6))


clear_temp_folder()