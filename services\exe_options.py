
from datetime import datetime
from typing import Iterable
from services.cod_esta.cod_est_utils import get_all_pdv_ec_for_active_branches, get_pdv_ecs_for_active_branches
from services.utils import divide_in_groups
from services.letters_dealer.letters_op_mult import upload_letters_in_multi_processes
from services.conections.update import update 
from modules.logger import create_logger
from services.scraping.scrape_multi import scrape_in_mult_processes


if __name__ == "__main__":
    options_logger = create_logger("",file_output="test_scrape.log")
    options_logger.setLevel(10)
else:
    options_logger = create_logger(__name__, without_handler = True)

RECOMMENDED_QTY_WORKER_FOR_DOWNLOAD = 5
RECOMMENDED_QTY_WORKER_FOR_UPLOAD = 8 


def status_letters_for_all_branches_by_day(sale_date:str, 
                                        workers_for_download: int = RECOMMENDED_QTY_WORKER_FOR_DOWNLOAD, 
                                        workers_for_upload: int = RECOMMENDED_QTY_WORKER_FOR_UPLOAD,
                                        branches_limit: int = None):
    
    date_obj = datetime.strptime(sale_date, "%d/%m/%Y")

    ecs = get_all_pdv_ec_for_active_branches(branches_limit)
    ec_for_tasks = divide_in_groups(ecs, workers_for_download)

    update_cosmos = update()
    tracker, msg = update_cosmos.update_reset_usuarios_controle()
    if tracker != None:
        options_logger.critical(msg)
        exit()
            
    scrape_in_mult_processes(ec_for_tasks, date_obj, options_logger.root.level)

    upload_letters_in_multi_processes(workers_for_upload)


def status_letters_for_branches_interval_by_day(sale_date:str,
                                            branches: Iterable[int], 
                                            workers_for_download: int = RECOMMENDED_QTY_WORKER_FOR_DOWNLOAD, 
                                            workers_for_upload: int = RECOMMENDED_QTY_WORKER_FOR_UPLOAD):
    
    date_obj = datetime.strptime(sale_date, "%d/%m/%Y")

    ecs = get_pdv_ecs_for_active_branches(branches)
    ec_for_tasks = divide_in_groups(ecs, workers_for_download)

    update_cosmos = update()
    tracker, msg = update_cosmos.update_reset_usuarios_controle()
    if tracker != None:
        options_logger.critical(msg)
        exit()
            
    scrape_in_mult_processes(ec_for_tasks, date_obj, options_logger.root.level)

    upload_letters_in_multi_processes(workers_for_upload)
    
    