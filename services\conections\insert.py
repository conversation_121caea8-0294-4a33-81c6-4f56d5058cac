import pyodbc
import services.vars as vars
from typing import Tuple
class insert:
    """Classe que controlar inserções no banco de dados
    """
    def __init__(self) -> None:
        driver = vars.driver
        server = vars.server
        database = vars.database
        uid = vars.uid
        pwd = vars.pwd
        mars_Connection = 'Yes'

        self.conn = pyodbc.connect(
            f'Driver={driver};Server={server};Database={database};UID={uid};PWD=*****;MARS_Connection{mars_Connection};')

        self.cursor = self.conn.cursor()    

    def insert_fluig_id(self,filial:str,id_pasta:str)->Tuple[int,str]:
        """
        Método responsável por inserir valores na tabela PASTAS_FLUIG

        Args:
            filial (str): String com o número da filial
            id_pasta (str): String com identificador da pasta no fluig
            

        Returns:
            Tuple[int,str]: Tupla com código de erro e mensagem"""
        
        try:

            query = f"""
            INSERT INTO cosmosrpa..PASTAS_FLUIG
            (FILIAL,ID_FLUIG) 
            VALUES({filial},{id_pasta})
            """  
            self.cursor.execute(query)
            self.cursor.execute("SELECT @@IDENTITY")
            id = self.cursor.fetchone()[0]
            self.conn.commit()
            
            msg = 'Valores Inseridos com Sucesso'
            return id,msg       

        except:
            return -2, "Erro ao inserir na tabela PASTAS_FLUIG"    

    def insert_letter(self,obj_carta:dict):
        """
        Método responsável por inserir valores na tabela  CARTAS_CANCELAMENTO

        Args:
            obj_carta (dict): Dicionário com os valores referentes a carta de cancelamento
            

        Returns:
            Tuple[int,str]: Tupla com código de erro e mensagem"""
        try:

            query = f"""
            INSERT INTO cosmosrpa..CARTAS_CANCELAMENTO
            (FILIAL
            ,VALOR_VENDA
            ,DATA_VENDA
            ,VALOR_CANCELAMENTO
            ,DATA_CANCELAMENTO
            ,NUMERO_CARTAO
            ,ESTABELECIMENTO
            ,CODIGO_AUTH
            ,PLANO_VENDA
            ,ID_FLUIG
            ,IN_FLUIG)
            VALUES
            ({obj_carta['FILIAL']}
            ,{obj_carta['VALOR_VENDA']}
            ,convert(date,'{obj_carta['DATA_VENDA']}')
            ,{obj_carta['VALOR_CANCELAMENTO']}
            ,convert(date,'{obj_carta['DATA_CANCELAMENTO']}')
            ,'{obj_carta['NUMERO_CARTAO']}'
            ,{obj_carta['ESTABELECIMENTO']}
            ,'{obj_carta['CODIGO_AUTH']}'
            ,'{obj_carta['PLANO_VENDA']}'
            ,{obj_carta['ID_FLUIG']}
            ,'Y')
            """  
            self.cursor.execute(query)
            self.cursor.execute("SELECT @@IDENTITY")
            id = self.cursor.fetchone()[0]
            self.conn.commit()
            
            msg = 'Valores Inseridos com Sucesso'
            return id,msg       

        except Exception as error:
            return -2, error

    def insert_undo_letter(self,obj_carta:object):
        """
        Método responsável por inserir valores na tabela CARTAS_DESFAZIMENTO

        Args:
            obj_carta (dict): Dicionário com os valores referentes a carta de desfazimento
            

        Returns:
            Tuple[int,str]: Tupla com código de erro e mensagem"""
        
        try:

            query = f"""
            INSERT INTO cosmosrpa..CARTAS_DESFAZIMENTO
            (FILIAL
            ,VALOR_VENDA
            ,DATA_VENDA
            ,NUMERO_CARTAO
            ,ESTABELECIMENTO
            ,NSU
            ,ID_FLUIG
            ,IN_FLUIG
            ,CREAT_AT)
            VALUES
            ({obj_carta['FILIAL']}
            ,{obj_carta['VALOR_VENDA']}
            ,'{obj_carta['DATA_VENDA']}'
            ,'{obj_carta['NUMERO_CARTAO']}'
            ,{obj_carta['ESTABELECIMENTO']}
            ,'{obj_carta['NSU']}'
            ,{obj_carta['ID_FLUIG']}
            ,'Y'
            ,GETDATE())
            """  
            
            self.cursor.execute(query)
            self.cursor.execute("SELECT @@IDENTITY")
            #id = self.cursor.fetchone()[0]
            self.conn.commit()
            
            msg = 'Valores Inseridos com Sucesso'
            return 0,msg       

        except Exception as error:
            return -2, error                