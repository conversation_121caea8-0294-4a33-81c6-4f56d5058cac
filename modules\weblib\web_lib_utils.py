

class ResultHandle():

    def __init__(self, success: bool, data, error_description: str):
        """ ### Controlar o fluxo de erros

        Args:
            success (bool): Booleano que marca se a operação ocorreu bem
            data (any): Resultado esperado
            error_description (str): Descriação do erro caso houver
            wait (bool): Deve
        """
        self.success = success
        self.error_description = error_description
        self.data = data
        self.wait = False

    @property
    def failure(self):
        return not self.success

    def __str__(self):
        if self.success:
            return f'[Success]'
        else:
            return f'[Failure]: "{self.error_description}"'

    def __repr__(self):
        if self.success:
            return f"<Result success={self.success}>"
        else:
            return f'<Result success={self.success}, message="{self.error_description}">'

    @classmethod
    def Fail(cls, error, data=None):
        return cls(False, data=data, error_description=error)

    @classmethod
    def Ok(cls, data=None):
        return cls(True, data=data, error_description=None)

    @classmethod
    def Waiting(cls, error = None, data=None):
        cls_obj = cls(False, data=data, error_description=error)
        cls_obj.wait = True
        return cls_obj