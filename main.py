from modules.logger import create_logger, create_log_file_within_execution_folder
from services.vars import log_folder
from services.utils import clear_temp_folder
from services.exe_options import status_letters_for_all_branches_by_day, status_letters_for_branches_interval_by_day
from datetime import datetime, timedelta


def main(date: str = None):

    if date is None:
        date = (datetime.today() - timedelta(days=1)).strftime("%d/%m/%Y")

    main_logger = create_logger('', 
                                file_output = create_log_file_within_execution_folder(log_folder, "principal.log"),
                                folder_by_execution= True)
    main_logger.setLevel(20)

    main_logger.info(f"Iniciando Cartas de desfazimento para o dia {date}")
    clear_temp_folder()
    
    status_letters_for_all_branches_by_day(date)

    main_logger.info("Fim Cartas de desfazimento")



if __name__ == "__main__":

    main("")

    
    





