from modules.logger import create_logger, create_log_file_within_execution_folder
from services.vars import log_folder
from services.utils import clear_temp_folder
from services.exe_options import status_letters_for_all_branches_by_day, status_letters_for_branches_interval_by_day
from datetime import datetime
import argparse



def main(date: str):
    """Executar Baixar cartas de desfazimento

    Args:
        date (str): Formatada como dd/mm/yyyy.
    """

    main_logger = create_logger('', 
                                file_output = create_log_file_within_execution_folder(log_folder, "principal.log"),
                                folder_by_execution= True)
    main_logger.setLevel(20)

    main_logger.info(f"Iniciando Cartas de desfazimento para o dia {date}")
    clear_temp_folder()
    
    status_letters_for_all_branches_by_day(date)

    main_logger.info("Fim Cartas de desfazimento")



def validate_date_format(date_string: str):
    try:
        datetime.strptime(date_string, "%d/%m/%Y")
        return date_string
    except ValueError:
        raise argparse.ArgumentTypeError(f"Argumento inválido: {date_string}. Experado no formato: dd/mm/yyyy")


if __name__ == "__main__":

    parser = argparse.ArgumentParser(description="Executar Baixar cartas de desfazimento")
    parser.add_argument("date", nargs='?', type=validate_date_format,
                       default=datetime.today().strftime("%d/%m/%Y"),
                       help="Data no formato dd/mm/yyyy (padrão: hoje)")

    args = parser.parse_args()
    main(args.date)

    
    





