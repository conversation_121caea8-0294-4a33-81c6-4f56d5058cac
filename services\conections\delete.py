import pyodbc
import vars

class delete:
    """Classe responsável por implementar métodos para realizar remoções do banco de dados
    """
    def __init__(self) -> None:
        """Construtor da classe
        """
        driver = vars.driver
        server = vars.server
        database = vars.database
        uid = vars.uid
        pwd = vars.pwd
        mars_Connection = 'Yes'

        self.conn = pyodbc.connect(
            f'Driver={driver};Server={server};Database={database};UID={uid};PWD=*****;MARS_Connection{mars_Connection};')

        self.cursor = self.conn.cursor()