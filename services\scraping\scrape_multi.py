
from datetime import datetime, timedelta
import logging.config
from typing import List, Dict
import logging
from time import sleep
from multiprocessing import Pool 
from multiprocessing.pool import AsyncResult
from services.scraping.scrape import scrape
from modules.logger import make_filehandler_for_folder_by_execution, create_logger, create_log_file_within_latest_exe_folder
from services.vars import log_folder
import sys


if __name__ == "__main__":
    scrape_mp_logger = create_logger("",)
    scrape_mp_logger.setLevel(10)
else:
    scrape_mp_logger = create_logger(__name__, without_handler = True)




def scrape_in_mult_processes(workers_tasks: List[List], 
                             date_obj: datetime,
                             logger_level: int = 20):

    results = []
    with Pool(processes=len(workers_tasks)) as p:
            
        tasks: List[AsyncResult] = []

        for worker_id, branches_to_scrap in enumerate(workers_tasks, 1):

            scrape_mp_logger.info(f"Iniciando worker {worker_id} de download")
            
            
            task = p.apply_async(scrape_worker, args=(branches_to_scrap, 
                                                    date_obj,
                                                    worker_id,
                                                    logger_level))
            tasks.append(task)

            if worker_id != len(workers_tasks):
                #Esperando o processo usar o arquivo geckodriver.exe
                sleep(5)    

            
        for index, task in enumerate(tasks, 1):
            try:
                res = task.get()
            except Exception as e:
                scrape_mp_logger.error(f"Erro no woker {index}")
                scrape_mp_logger.error(f"Detalhe do erro: {e}")
                continue
            
            results.append(res)
    
    return results



def scrape_worker(ecs: List[Dict], 
           sale_date: datetime = (datetime.today() - timedelta(days=1)),
           worker_id: int = 1,
           logger_level: int = 20):

    
    worker_logger = logging.getLogger()
    file_handlers = make_filehandler_for_folder_by_execution(create_log_file_within_latest_exe_folder(log_folder, f"Worker-{worker_id}.log"))
    worker_logger.addHandler(file_handlers)
    worker_logger.setLevel(logger_level)

    worker_logger.info(f'Worker {worker_id} iniciando')
    try:
        scrape(ecs, sale_date)
    except Exception as e:
        worker_logger.error(f"Erro no Worker {worker_id} de download")
        worker_logger.error(f"Detalhes do erro {e}")

    finally:
        worker_logger.info(f'Worker {worker_id} finalizado')


 

